{"logs": [{"outputFile": "com.synaptix.app-mergeDebugResources-55:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ed22a0039ae602719136455601f8f0f\\transformed\\react-android-0.79.4-debug\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,193,260,326,401,466,532,602,674,747,822,889,959,1032,1104,1181,1257,1329,1399,1468,1548,1616,1686,1753", "endColumns": "65,71,66,65,74,64,65,69,71,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "116,188,255,321,396,461,527,597,669,742,817,884,954,1027,1099,1176,1252,1324,1394,1463,1543,1611,1681,1748,1817"}, "to": {"startLines": "33,49,88,95,96,98,112,113,114,162,163,164,165,170,171,172,173,174,175,176,177,179,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2863,4204,7903,8286,8352,8487,9387,9453,9523,12927,13000,13075,13142,13509,13582,13654,13731,13807,13879,13949,14018,14199,14267,14337,14404", "endColumns": "65,71,66,65,74,64,65,69,71,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "2924,4271,7965,8347,8422,8547,9448,9518,9590,12995,13070,13137,13207,13577,13649,13726,13802,13874,13944,14013,14093,14262,14332,14399,14468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9a19d447f99dacc987e9045d6bd5cdca\\transformed\\browser-1.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,139,231,332", "endColumns": "83,91,100,92", "endOffsets": "134,226,327,420"}, "to": {"startLines": "69,75,76,77", "startColumns": "4,4,4,4", "startOffsets": "6193,6602,6694,6795", "endColumns": "83,91,100,92", "endOffsets": "6272,6689,6790,6883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ef88a174ce7b920cee45b0a5e12a3fac\\transformed\\play-services-basement-18.3.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "98", "endOffsets": "297"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5140", "endColumns": "102", "endOffsets": "5238"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6550f91160cccf4761958ba318c6d009\\transformed\\material-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,912,974,1052,1112,1172,1250,1311,1369,1425,1485,1543,1597,1682,1738,1796,1850,1915,2007,2081,2153,2235,2309,2386,2506,2569,2632,2731,2808,2882,2932,2983,3049,3112,3180,3251,3322,3383,3454,3521,3583,3670,3749,3814,3897,3982,4056,4120,4196,4244,4317,4381,4457,4535,4597,4661,4724,4790,4870,4948,5024,5103,5157,5212,5281,5356,5429", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,63,61,66,69,76,93,106,72,50,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,71,81,73,76,119,62,62,98,76,73,49,50,65,62,67,70,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68,74,72,69", "endOffsets": "242,306,368,435,505,582,676,783,856,907,969,1047,1107,1167,1245,1306,1364,1420,1480,1538,1592,1677,1733,1791,1845,1910,2002,2076,2148,2230,2304,2381,2501,2564,2627,2726,2803,2877,2927,2978,3044,3107,3175,3246,3317,3378,3449,3516,3578,3665,3744,3809,3892,3977,4051,4115,4191,4239,4312,4376,4452,4530,4592,4656,4719,4785,4865,4943,5019,5098,5152,5207,5276,5351,5424,5494"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,72,73,74,94,97,99,100,101,102,103,104,105,106,107,108,109,110,111,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2929,2993,3055,3122,3192,3930,4024,4131,6411,6462,6524,8226,8427,8552,8630,8691,8749,8805,8865,8923,8977,9062,9118,9176,9230,9295,9595,9669,9741,9823,9897,9974,10094,10157,10220,10319,10396,10470,10520,10571,10637,10700,10768,10839,10910,10971,11042,11109,11171,11258,11337,11402,11485,11570,11644,11708,11784,11832,11905,11969,12045,12123,12185,12249,12312,12378,12458,12536,12612,12691,12745,12800,13291,13366,13439", "endLines": "5,34,35,36,37,38,46,47,48,72,73,74,94,97,99,100,101,102,103,104,105,106,107,108,109,110,111,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,167,168,169", "endColumns": "12,63,61,66,69,76,93,106,72,50,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,71,81,73,76,119,62,62,98,76,73,49,50,65,62,67,70,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68,74,72,69", "endOffsets": "292,2988,3050,3117,3187,3264,4019,4126,4199,6457,6519,6597,8281,8482,8625,8686,8744,8800,8860,8918,8972,9057,9113,9171,9225,9290,9382,9664,9736,9818,9892,9969,10089,10152,10215,10314,10391,10465,10515,10566,10632,10695,10763,10834,10905,10966,11037,11104,11166,11253,11332,11397,11480,11565,11639,11703,11779,11827,11900,11964,12040,12118,12180,12244,12307,12373,12453,12531,12607,12686,12740,12795,12864,13361,13434,13504"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\97ed3adf425b530d685cdd8470e3d9b5\\transformed\\core-1.13.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "39,40,41,42,43,44,45,178", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3269,3361,3460,3554,3648,3741,3834,14098", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3356,3455,3549,3643,3736,3829,3925,14194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f64dc84a821f94336e410d15a520d5cf\\transformed\\appcompat-1.7.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,13212", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,13286"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d29e10314c2a4b4c7355c13794550e06\\transformed\\play-services-base-18.0.1\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,938,1029,1135,1232,1357,1468,1566,1670,1722,1775", "endColumns": "96,123,110,97,102,111,95,90,105,96,124,110,97,103,51,52,69", "endOffsets": "293,417,528,626,729,841,937,1028,1134,1231,1356,1467,1565,1669,1721,1774,1844"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4276,4377,4505,4620,4722,4829,4945,5045,5243,5353,5454,5583,5698,5800,5908,5964,6021", "endColumns": "100,127,114,101,106,115,99,94,109,100,128,114,101,107,55,56,73", "endOffsets": "4372,4500,4615,4717,4824,4940,5040,5135,5348,5449,5578,5693,5795,5903,5959,6016,6090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ac4000ff40bdee0ae650966147dfc40c\\transformed\\biometric-1.1.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,235,334,437,540,638,739,843,933,1045,1152", "endColumns": "97,81,98,102,102,97,100,103,89,111,106,97", "endOffsets": "148,230,329,432,535,633,734,838,928,1040,1147,1245"}, "to": {"startLines": "68,71,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6095,6329,6888,6987,7090,7193,7291,7392,7496,7586,7698,7805", "endColumns": "97,81,98,102,102,97,100,103,89,111,106,97", "endOffsets": "6188,6406,6982,7085,7188,7286,7387,7491,7581,7693,7800,7898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f539835b4c97e6ac057f1617bca8ceec\\transformed\\android-image-cropper-4.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,107,149,206,261,313,363", "endColumns": "51,41,56,54,51,49,57", "endOffsets": "102,144,201,256,308,358,416"}, "to": {"startLines": "70,89,90,91,92,93,161", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6277,7970,8012,8069,8124,8176,12869", "endColumns": "51,41,56,54,51,49,57", "endOffsets": "6324,8007,8064,8119,8171,8221,12922"}}]}]}
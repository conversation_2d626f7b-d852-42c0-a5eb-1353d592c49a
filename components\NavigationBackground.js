import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useSettings } from '../contexts/SettingsContext';

/**
 * A wrapper component that provides a consistent background color
 * for the navigation container to prevent white flashes during transitions
 */
const NavigationBackground = ({ children }) => {
  const { darkModeEnabled } = useSettings();

  return (
    <View style={[
      styles.container,
      { backgroundColor: darkModeEnabled ? '#121212' : '#f5f5f5' }
    ]}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default NavigationBackground;

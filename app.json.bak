{"expo": {"plugins": ["react-native-ble-plx"], "android": {"package": "com.synaptix.app", "permissions": ["BLUETOOTH", "BLUETOOTH_ADMIN", "ACCESS_FINE_LOCATION", "BLUETOOTH_CONNECT", "BLUETOOTH_SCAN"]}, "name": "Synaptix", "slug": "Synaptix", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": false, "splash": {"image": "./assets/logo_splashscreen-removebg-preview.png", "resizeMode": "contain", "backgroundColor": "#121212"}, "doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}, "jsEngine": "jsc", "scheme": "synaptix", "ios": {"supportsTablet": true, "bundleIdentifier": "com.synaptix.app"}, "web": {"favicon": "./assets/favicon.png"}}}
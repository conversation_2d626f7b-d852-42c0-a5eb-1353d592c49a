# Arduino Nano 33 BLE Examples for Synaptix App

This directory contains example Arduino sketches that simulate EEG data and send it over Bluetooth Low Energy to the Synaptix app.

## Hardware Requirements

- Arduino Nano 33 BLE or Arduino Nano 33 BLE Sense
- USB cable for programming

## Software Requirements

- Arduino IDE (1.8.13 or later)
- ArduinoBLE library (install via Arduino Library Manager)

## Examples

### 1. EEGSimulator

This example sends simulated EEG data in binary format (16-bit integers). It's more efficient but harder to debug.

### 2. EEGSimulatorCSV

This example sends simulated EEG data in CSV string format. It's easier to debug but less efficient.

## Setup Instructions

1. Install the Arduino IDE from [arduino.cc](https://www.arduino.cc/en/software)
2. Install the Arduino Nano 33 BLE board support:
   - Open Arduino IDE
   - Go to Tools > Board > Boards Manager
   - Search for "Arduino Nano 33 BLE"
   - Install the package
3. Install the ArduinoBLE library:
   - Go to Sketch > Include Library > Manage Libraries
   - Search for "ArduinoBLE"
   - Install the library
4. Connect your Arduino Nano 33 BLE to your computer
5. Open one of the example sketches
6. Select the correct board: Tools > Board > Arduino Nano 33 BLE
7. Select the correct port: Tools > Port > (select the port with Arduino Nano 33 BLE)
8. Upload the sketch to your Arduino

## Using with the Synaptix App

1. Upload one of the example sketches to your Arduino Nano 33 BLE
2. Open the Synaptix app on your mobile device
3. Go to the Connect screen
4. Scan for Bluetooth devices
5. Connect to "EEG-Nano33" or "EEG-Nano33-CSV" depending on which example you uploaded
6. The app should now receive the simulated EEG data

## Troubleshooting

- If the device doesn't appear in the scan list, make sure Bluetooth is enabled on your mobile device
- If the connection fails, try resetting the Arduino and scanning again
- Check the Arduino Serial Monitor (9600 baud) for debugging information
- Make sure the UUIDs in the Arduino sketch match the UUIDs in the app

## Customizing

You can modify these examples to work with real EEG sensors by:

1. Connecting your EEG sensors to the analog or digital pins of the Arduino Nano 33 BLE
2. Replacing the random data generation with actual sensor readings
3. Adjusting the data format to match your sensor's output

## License

These examples are released under the MIT License.

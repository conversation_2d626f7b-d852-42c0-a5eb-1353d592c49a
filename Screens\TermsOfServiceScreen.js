import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import CustomHeader from '../components/CustomHeader';
import { ensureStatusBarAppearance } from '../utils/screenTransition';
import { useSettings } from '../contexts/SettingsContext';

export default function TermsOfServiceScreen() {
  const navigation = useNavigation();
  const { darkModeEnabled } = useSettings();

  // Ensure StatusBar is properly configured when the screen mounts
  useEffect(() => {
    ensureStatusBarAppearance(darkModeEnabled);
  }, [darkModeEnabled]);

  const handleBackPress = () => {
    navigation.goBack();
  };

  return (
    <View style={[
      styles.container,
      { backgroundColor: darkModeEnabled ? '#121212' : '#f5f5f5' }
    ]}>
      <StatusBar barStyle={darkModeEnabled ? "light-content" : "dark-content"} />
      <CustomHeader
        title="Terms of Service"
        showProfileIcon={false}
        showBackButton={true}
        onBackPress={handleBackPress} />

      <ScrollView style={styles.scrollContainer}>
        <View style={styles.contentContainer}>
          <View style={[
            styles.card,
            { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
          ]}>
            <Text style={[
              styles.paragraph,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              Welcome to Synaptix! By using this app, you agree to the following Terms of Service:
            </Text>

            <Text style={[
              styles.sectionTitle,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>1. Use of the App</Text>
            <Text style={[
              styles.paragraph,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              You must:
            </Text>
            <View style={styles.bulletList}>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• Be at least 13 years old.</Text>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• Use the app only for lawful and personal health-related purposes.</Text>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• Not misuse the app to reverse-engineer or disrupt its services.</Text>
            </View>

            <Text style={[
              styles.sectionTitle,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>2. EEG Signal Analysis Disclaimer</Text>
            <View style={styles.bulletList}>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• The app provides informational analysis only, not medical advice.</Text>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• Always consult a licensed medical professional before making health decisions.</Text>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• We are not liable for any decisions or actions taken based on the app's output.</Text>
            </View>

            <Text style={[
              styles.sectionTitle,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>3. User Accounts</Text>
            <View style={styles.bulletList}>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• You are responsible for keeping your login credentials safe.</Text>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• We reserve the right to suspend or terminate accounts for policy violations.</Text>
            </View>

            <Text style={[
              styles.sectionTitle,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>4. Intellectual Property</Text>
            <Text style={[
              styles.paragraph,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              All rights, titles, and interests in the app, its design, and algorithm remain with Synaptix.
            </Text>

            <Text style={[
              styles.sectionTitle,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>5. Limitation of Liability</Text>
            <Text style={[
              styles.paragraph,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              We are not responsible for:
            </Text>
            <View style={styles.bulletList}>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• Loss of data.</Text>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• Inaccuracies in analysis due to poor EEG signal quality or device malfunction.</Text>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• Any indirect or consequential damages from the use of the app.</Text>
            </View>

            <Text style={[
              styles.sectionTitle,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>6. Termination</Text>
            <Text style={[
              styles.paragraph,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              We reserve the right to terminate or suspend access to the app without notice for violating these terms.
            </Text>

            <Text style={[
              styles.sectionTitle,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>7. Governing Law</Text>
            <Text style={[
              styles.paragraph,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              These terms are governed by the laws of India.
            </Text>

            <Text style={[
              styles.contactSection,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              Contact Us:
            </Text>
            <Text style={[
              styles.paragraph,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              For questions or concerns, email <NAME_EMAIL>
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  card: {
    borderRadius: 10,
    padding: 20,
  },
  effectiveDate: {
    fontSize: 14,
    marginBottom: 20,
    fontStyle: 'italic',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 25,
    marginBottom: 10,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 15,
  },
  bulletList: {
    marginLeft: 10,
    marginBottom: 15,
  },
  bulletItem: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 8,
  },
  contactSection: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 30,
    marginBottom: 10,
  },
});

module.exports = function(api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      // Ensure polyfills are properly resolved
      ['module-resolver', {
        alias: {
          'stream': 'stream-browserify',
          'crypto': 'crypto-browserify',
          'http': '@tradle/react-native-http',
          'https': 'https-browserify',
          'os': 'os-browserify/browser.js',
          'path': 'path-browserify',
          'zlib': 'browserify-zlib',
          'url': 'whatwg-url',
          'buffer': 'buffer',
          'process': 'process/browser',
          'events': 'events',
          'util': 'util',
          'string_decoder': 'string_decoder',
        },
      }],
    ],
    // Ensure we don't strip out polyfills during optimization
    assumptions: {
      noDocumentAll: false,
      noClassCalls: false,
    },
  };
};

const { getDefaultConfig } = require('expo/metro-config');

// Get the default config
const defaultConfig = getDefaultConfig(__dirname);

// Add resolution for the 'stream' module and other Node.js modules
// Only include the modules we actually need for Supabase and other functionality
defaultConfig.resolver.extraNodeModules = {
  ...defaultConfig.resolver.extraNodeModules,
  stream: require.resolve('stream-browserify'),
  crypto: require.resolve('crypto-browserify'),
  buffer: require.resolve('buffer'),
  process: require.resolve('process/browser'),
  events: require.resolve('events'),
  util: require.resolve('util'),
  url: require.resolve('whatwg-url'),
};

// Add additional asset extensions
defaultConfig.resolver.assetExts = [...defaultConfig.resolver.assetExts, 'db', 'sqlite'];

// Add support for mjs files
defaultConfig.resolver.sourceExts = [...defaultConfig.resolver.sourceExts, 'mjs'];

module.exports = defaultConfig;

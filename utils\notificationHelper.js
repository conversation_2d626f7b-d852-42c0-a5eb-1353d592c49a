import { Platform } from 'react-native';

// Notification functionality has been disabled
// This is a stub implementation that doesn't use actual notifications
console.log('Notification functionality is disabled');

/**
 * Request notification permissions (stub)
 * @returns {Promise<boolean>} Always returns true
 */
export const requestNotificationPermissions = async () => {
  console.log('Notification permissions requested (stub)');
  return true;
};

/**
 * Check if notification permissions are granted (stub)
 * @returns {Promise<boolean>} Always returns true
 */
export const checkNotificationPermissions = async () => {
  console.log('Checking notification permissions (stub)');
  return true;
};

/**
 * Schedule a local notification (stub)
 * @param {string} title Notification title
 * @param {string} body Notification body
 * @param {Object} data Additional data to include with the notification
 * @param {number} seconds Seconds from now to schedule the notification
 * @returns {Promise<string|null>} Mock notification identifier
 */
export const scheduleNotification = async (title, body, data = {}, seconds = 5) => {
  console.log(`[NOTIFICATION STUB] Would schedule: "${title}" - ${body} in ${seconds} seconds`);
  return 'mock-notification-id';
};

/**
 * Send an immediate notification (stub)
 * @param {string} title Notification title
 * @param {string} body Notification body
 * @param {Object} data Additional data to include with the notification
 * @returns {Promise<string|null>} Mock notification identifier
 */
export const sendImmediateNotification = async (title, body, data = {}) => {
  console.log(`[NOTIFICATION STUB] Would send immediately: "${title}" - ${body}`);
  return 'mock-notification-id';
};

/**
 * Cancel all scheduled notifications (stub)
 * @returns {Promise<void>}
 */
export const cancelAllNotifications = async () => {
  console.log('[NOTIFICATION STUB] Would cancel all notifications');
};

/**
 * Register for push notifications (stub)
 * @returns {Promise<string|null>} Mock push token
 */
export const registerForPushNotifications = async () => {
  console.log('[NOTIFICATION STUB] Would register for push notifications');
  return 'mock-push-token';
};

export default {
  requestNotificationPermissions,
  checkNotificationPermissions,
  scheduleNotification,
  sendImmediateNotification,
  cancelAllNotifications,
  registerForPushNotifications,
};

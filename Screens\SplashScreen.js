import { useEffect } from 'react';
import { View, Text, StyleSheet, Image, StatusBar } from 'react-native';
import { useSettings } from '../contexts/SettingsContext';

export default function SplashScreen({ onFinish }) {
  const { darkModeEnabled } = useSettings();
  useEffect(() => {
    // Set a timeout to call the onFinish callback after 1.5 seconds
    const timer = setTimeout(() => {
      onFinish();
    }, 1500);

    // Clear the timeout if the component unmounts
    return () => clearTimeout(timer);
  }, [onFinish]);

  return (
    <View style={[
      styles.container,
      { backgroundColor: darkModeEnabled ? '#121212' : '#f5f5f5' }
    ]}>
      <StatusBar barStyle={darkModeEnabled ? "light-content" : "dark-content"} />

      {/* Main content container with centered logo */}
      <View style={styles.contentContainer}>
        <View style={styles.logoContainer}>
          <Image
            source={require('../assets/logo_splashscreen-removebg-preview.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>
      </View>

      <View style={styles.footerContainer}>
        <Text style={[
          styles.fromText,
          { color: darkModeEnabled ? '#9e9e9e' : '#757575' }
        ]}>from</Text>
        <Text style={styles.companyText}>InnovateX</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // Background color is set dynamically
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    width: 100,
    height: 100,
  },
  footerContainer: {
    position: 'absolute',
    bottom: 55, // Moved up by 5 pixels (from 50 to 55)
    left: 0,
    right: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fromText: {
    fontSize: 16,
    color: '#9e9e9e', // Dark gray color for "from"
    marginBottom: 5,
  },
  companyText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#d442f5', // Pink-to-purple color for "InnovateX" to match the logo
  },
});

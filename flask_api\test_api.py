#!/usr/bin/env python3
# Make this script executable with: chmod +x test_api.py
"""
Test script for the Synaptix Flask API
"""

import requests
import json
import time
import random

# API base URL - change this to your Flask server URL
API_BASE_URL = 'http://127.0.0.1:5000/api'

def test_health_check():
    """Test the health check endpoint"""
    print("\n=== Testing Health Check Endpoint ===")
    try:
        response = requests.get(f'{API_BASE_URL}/health')
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")

        if response.status_code == 200:
            print("✅ Health check successful")
            return True
        else:
            print("❌ Health check failed")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_predict_with_mock_data():
    """Test the predict endpoint with mock data"""
    print("\n=== Testing Predict Endpoint with Mock Data ===")

    # Create mock EEG data
    mock_data = {
        "eegData": {
            "timestamp": int(time.time() * 1000),
            "channels": [
                random.randint(100, 200),
                random.randint(100, 200),
                random.randint(100, 200),
                random.randint(100, 200),
                random.randint(100, 200),
                random.randint(100, 200),
                random.randint(100, 200),
                random.randint(100, 200)
            ]
        }
    }

    try:
        response = requests.post(
            f'{API_BASE_URL}/predict',
            json=mock_data,
            headers={'Content-Type': 'application/json'}
        )

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("\nPrediction Results:")
            print(f"Overall Score: {result['overallScore']}")

            print("\nCategories:")
            for category in result['categories']:
                print(f"- {category['name']}: {category['score']}%")

            print("\nRecommendations:")
            for recommendation in result['recommendations']:
                print(f"- {recommendation}")

            print("\n✅ Prediction test successful")
            return True
        else:
            print(f"❌ Prediction test failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run all tests"""
    print("=== Synaptix Flask API Test ===")

    # Test health check
    health_check_success = test_health_check()

    # Test predict endpoint
    if health_check_success:
        predict_success = test_predict_with_mock_data()
    else:
        print("\n❌ Skipping prediction test because health check failed")
        predict_success = False

    # Print summary
    print("\n=== Test Summary ===")
    print(f"Health Check: {'✅ Passed' if health_check_success else '❌ Failed'}")
    print(f"Prediction: {'✅ Passed' if predict_success else '❌ Failed'}")

    if health_check_success and predict_success:
        print("\n✅ All tests passed! The API is working correctly.")
    else:
        print("\n❌ Some tests failed. Please check the API server.")

if __name__ == "__main__":
    main()

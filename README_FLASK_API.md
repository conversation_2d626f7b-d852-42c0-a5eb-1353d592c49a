# Synaptix Flask API Integration

This document explains how to set up and use the Flask API integration for the Synaptix Brain App.

## Overview

The Flask API integration allows the Synaptix app to send EEG data to a Flask server for processing and receive prediction results. This enables more advanced analysis of brain data that might be too computationally intensive to perform on the mobile device.

## Setup

### Prerequisites

- Python 3.7 or higher
- Node.js and npm (for running the React Native app)
- Expo CLI (for running the React Native app)

### Flask API Server Setup

1. Navigate to the `flask_api` directory:
```
cd flask_api
```

2. Install the required Python packages:
```
pip install -r requirements.txt
```

3. Run the Flask server:
   - On Windows: Run the `run_server.bat` script
   - On macOS/Linux: Run the `run_server.sh` script (make it executable first with `chmod +x run_server.sh`)

The Flask server will start on port 5000 by default. You can change this by setting the `PORT` environment variable.

### React Native App Setup

1. Update the API base URL in `utils/apiService.js` to point to your Flask server:
```javascript
// For local development with Expo, use your computer's IP address
const API_BASE_URL = 'http://YOUR_IP_ADDRESS:5000/api';
```

2. Install the required npm packages:
```
npm install
```

3. Start the Expo development server:
```
npx expo start
```

## How It Works

1. The Synaptix app connects to an EEG device via Bluetooth.
2. When EEG data is received, it is sent to the Flask API for processing.
3. The Flask API processes the data and returns prediction results.
4. The app displays the prediction results on the Result screen.

## API Endpoints

### Health Check
```
GET /api/health
```
Returns the health status of the API.

### Predict
```
POST /api/predict
```
Processes EEG data and returns prediction results.

#### Request Body
```json
{
  "eegData": {
    "channels": [array of channel values],
    "timestamp": timestamp
  }
}
```

#### Response
```json
{
  "overallScore": number,
  "categories": [
    {
      "name": string,
      "score": number,
      "color": string
    }
  ],
  "recommendations": [array of strings]
}
```

## Troubleshooting

### Flask API Server Issues

- Make sure Python and pip are installed correctly.
- Check that all required packages are installed.
- Verify that the server is running on the expected port.
- Check the server logs for any error messages.

### React Native App Issues

- Make sure the API base URL in `utils/apiService.js` is correct.
- Check the app logs for any API-related error messages.
- Verify that the device has network connectivity.
- Try using the fallback local data processing if the API is unavailable.

## Extending the API

You can extend the Flask API to add more advanced features:

1. Add new endpoints in `app.py`.
2. Update the API service in `utils/apiService.js` to use the new endpoints.
3. Modify the app to use the new functionality.

For example, you could add endpoints for:
- Storing EEG data for later analysis
- Comparing current results with historical data
- Generating more detailed reports
- Implementing machine learning models for more accurate predictions

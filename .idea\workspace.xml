<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ed636c37-2f13-47a6-8b4f-c7060c5d635c" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/App.js" beforeDir="false" afterPath="$PROJECT_DIR$/App.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Navigation/AppNavigatorNew.js" beforeDir="false" afterPath="$PROJECT_DIR$/Navigation/AppNavigatorNew.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Screens/ConnectScreen.js" beforeDir="false" afterPath="$PROJECT_DIR$/Screens/ConnectScreen.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Screens/EditProfileScreen.js" beforeDir="false" afterPath="$PROJECT_DIR$/Screens/EditProfileScreen.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Screens/HelpCenterScreen.js" beforeDir="false" afterPath="$PROJECT_DIR$/Screens/HelpCenterScreen.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Screens/HistoryScreen.js" beforeDir="false" afterPath="$PROJECT_DIR$/Screens/HistoryScreen.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Screens/HomeScreen.js" beforeDir="false" afterPath="$PROJECT_DIR$/Screens/HomeScreen.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Screens/LoginScreen.js" beforeDir="false" afterPath="$PROJECT_DIR$/Screens/LoginScreen.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Screens/ResultScreen.js" beforeDir="false" afterPath="$PROJECT_DIR$/Screens/ResultScreen.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Screens/SignupScreen.js" beforeDir="false" afterPath="$PROJECT_DIR$/Screens/SignupScreen.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app.config.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app.json" beforeDir="false" afterPath="$PROJECT_DIR$/app.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/babel.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/babel.config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/components/WhiteButton.js" beforeDir="false" afterPath="$PROJECT_DIR$/components/WhiteButton.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/contexts/AuthContext.js" beforeDir="false" afterPath="$PROJECT_DIR$/contexts/AuthContext.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/contexts/SettingsContext.js" beforeDir="false" afterPath="$PROJECT_DIR$/contexts/SettingsContext.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/eas.json" beforeDir="false" afterPath="$PROJECT_DIR$/eas.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/metro.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/metro.config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/polyfills.js" beforeDir="false" afterPath="$PROJECT_DIR$/polyfills.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/utils/androidNotificationConfig.js" beforeDir="false" afterPath="$PROJECT_DIR$/utils/androidNotificationConfig.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/utils/apiService.js" beforeDir="false" afterPath="$PROJECT_DIR$/utils/apiService.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/utils/notificationHelper.js" beforeDir="false" afterPath="$PROJECT_DIR$/utils/notificationHelper.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/utils/supabase-native.js" beforeDir="false" afterPath="$PROJECT_DIR$/utils/supabase-native.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/utils/supabaseClient.js" beforeDir="false" afterPath="$PROJECT_DIR$/utils/supabaseClient.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 7
}]]></component>
  <component name="ProjectId" id="2xJ0OJEodCx7hVpqXIRPq20YpRw" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "C:/Users/<USER>/OneDrive/Desktop/FOCUS AI/FocusAI/android"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ed636c37-2f13-47a6-8b4f-c7060c5d635c" name="Changes" comment="" />
      <created>1747640835966</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747640835966</updated>
    </task>
    <servers />
  </component>
</project>
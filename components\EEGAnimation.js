import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useSettings } from '../contexts/SettingsContext';

/**
 * EEG Animation Component
 *
 * Displays an animated EEG waveform in a container
 * The animation uses multiple lines with different phases to create a realistic EEG effect
 */
const EEGAnimation = ({ style }) => {
  const { darkModeEnabled } = useSettings();

  // Animation values for each wave
  const wave1 = useRef(new Animated.Value(0)).current;
  const wave2 = useRef(new Animated.Value(0)).current;
  const wave3 = useRef(new Animated.Value(0)).current;
  const wave4 = useRef(new Animated.Value(0)).current;
  const wave5 = useRef(new Animated.Value(0)).current;

  // Start the animation when the component mounts
  useEffect(() => {
    // Create infinite loop animations for each wave with different durations
    const createLoopAnimation = (value, duration) => {
      Animated.loop(
        Animated.timing(value, {
          toValue: 1,
          duration: duration,
          useNativeDriver: true,
        })
      ).start();
    };

    // Start animations with different durations to create varied effect
    createLoopAnimation(wave1, 3000);
    createLoopAnimation(wave2, 4000);
    createLoopAnimation(wave3, 3500);
    createLoopAnimation(wave4, 4500);
    createLoopAnimation(wave5, 5000);

    // Clean up animations when component unmounts
    return () => {
      wave1.stopAnimation();
      wave2.stopAnimation();
      wave3.stopAnimation();
      wave4.stopAnimation();
      wave5.stopAnimation();
    };
  }, []);

  // Create the wave components
  const renderWave = (animValue, index, totalWaves) => {
    // Calculate vertical position based on index
    const yPosition = `${(index / (totalWaves - 1)) * 100}%`;

    // Interpolate animation value to create wave effect
    const translateX = animValue.interpolate({
      inputRange: [0, 1],
      outputRange: ['-100%', '100%'],
    });

    return (
      <Animated.View
        key={index}
        style={[
          styles.wave,
          {
            top: yPosition,
            transform: [{ translateX }],
            backgroundColor: darkModeEnabled ? '#9C27B0' : '#9C27B0',
            opacity: darkModeEnabled ? 0.3 : 0.2,
          },
        ]}
      />
    );
  };

  // Create the EEG spikes
  const renderSpikes = (animValue, index, totalWaves) => {
    // Calculate vertical position based on index
    const yPosition = `${(index / (totalWaves - 1)) * 100}%`;

    // Interpolate animation value to create spike effect
    const translateX = animValue.interpolate({
      inputRange: [0, 0.2, 0.3, 0.4, 0.6, 0.7, 0.8, 1],
      outputRange: ['-100%', '-50%', '-30%', '0%', '20%', '40%', '70%', '100%'],
    });

    const scaleY = animValue.interpolate({
      inputRange: [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1],
      outputRange: [1, 1.5, 1, 2, 1, 1.5, 1, 2.5, 1, 1.5, 1],
    });

    return (
      <Animated.View
        key={`spike-${index}`}
        style={[
          styles.spike,
          {
            top: yPosition,
            transform: [
              { translateX },
              { scaleY }
            ],
            backgroundColor: darkModeEnabled ? '#E91E63' : '#E91E63',
            opacity: darkModeEnabled ? 0.4 : 0.3,
          },
        ]}
      />
    );
  };

  return (
    <LinearGradient
      colors={darkModeEnabled ? ['#4527A0', '#5E35B1'] : ['#5E35B1', '#7E57C2']}
      style={[styles.container, style]}
    >
      {/* Overlay gradient for depth effect */}
      <LinearGradient
        colors={['rgba(0,0,0,0.1)', 'rgba(0,0,0,0)']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.overlay}
      />

      {/* Base waves */}
      {renderWave(wave1, 0, 5)}
      {renderWave(wave2, 1, 5)}
      {renderWave(wave3, 2, 5)}
      {renderWave(wave4, 3, 5)}
      {renderWave(wave5, 4, 5)}

      {/* EEG spikes */}
      {renderSpikes(wave1, 0, 5)}
      {renderSpikes(wave2, 1, 5)}
      {renderSpikes(wave3, 2, 5)}
      {renderSpikes(wave4, 3, 5)}
      {renderSpikes(wave5, 4, 5)}
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: '100%',
    borderRadius: 15,
    overflow: 'hidden',
    position: 'relative',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  wave: {
    position: 'absolute',
    height: 2,
    width: '200%', // Make wider than container to allow for animation
    left: 0,
    zIndex: 2,
  },
  spike: {
    position: 'absolute',
    height: 3,
    width: '200%', // Make wider than container to allow for animation
    left: 0,
    zIndex: 3,
  }
});

export default EEGAnimation;

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSettings } from '../contexts/SettingsContext';

/**
 * A modal component that asks for user permission to save their data
 * Shown after successful registration
 */
export default function DataPermissionPrompt({
  visible,
  onAccept,
  onDecline,
  onViewPrivacyPolicy,
  user
}) {
  const { darkModeEnabled } = useSettings();

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onDecline}
    >
      <View style={styles.centeredView}>
        <View style={[
          styles.modalView,
          darkModeEnabled
            ? { backgroundColor: '#2a2a2a', borderColor: '#3a3a3a', borderWidth: 1 }
            : { backgroundColor: '#ffffff' }
        ]}>
          <View style={styles.header}>
            <Text style={[
              styles.headerText,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>Data Privacy Consent</Text>
          </View>

          <View style={styles.iconContainer}>
            <Ionicons
              name="shield-checkmark-outline"
              size={50}
              color={darkModeEnabled ? "#6a6a6a" : "#7B68EE"}
            />
          </View>

          <ScrollView style={styles.scrollContainer}>
            <Text style={[
              styles.infoText,
              { color: darkModeEnabled ? '#e0e0e0' : '#333333' }
            ]}>
              Welcome to Synaptix! We value your privacy and want to be transparent about how we use your data.
            </Text>

            <Text style={[
              styles.infoText,
              { color: darkModeEnabled ? '#e0e0e0' : '#333333' }
            ]}>
              By accepting, you allow us to:
            </Text>

            <View style={styles.bulletPoints}>
              <View style={styles.bulletPoint}>
                <Ionicons
                  name="checkmark-circle"
                  size={20}
                  color={darkModeEnabled ? "#6a6a6a" : "#7B68EE"}
                />
                <Text style={[
                  styles.bulletText,
                  { color: darkModeEnabled ? '#e0e0e0' : '#333333' }
                ]}>
                  Store your account information securely
                </Text>
              </View>

              <View style={styles.bulletPoint}>
                <Ionicons
                  name="checkmark-circle"
                  size={20}
                  color={darkModeEnabled ? "#6a6a6a" : "#7B68EE"}
                />
                <Text style={[
                  styles.bulletText,
                  { color: darkModeEnabled ? '#e0e0e0' : '#333333' }
                ]}>
                  Process your EEG data for analysis
                </Text>
              </View>

              <View style={styles.bulletPoint}>
                <Ionicons
                  name="checkmark-circle"
                  size={20}
                  color={darkModeEnabled ? "#6a6a6a" : "#7B68EE"}
                />
                <Text style={[
                  styles.bulletText,
                  { color: darkModeEnabled ? '#e0e0e0' : '#333333' }
                ]}>
                  Use anonymized data to improve our services
                </Text>
              </View>
            </View>

            <Text style={[
              styles.infoText,
              { color: darkModeEnabled ? '#e0e0e0' : '#333333' }
            ]}>
              You can change your data preferences anytime in Settings. For more details, see our Privacy Policy.
            </Text>
          </ScrollView>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[
                styles.acceptButton,
                darkModeEnabled
                  ? { backgroundColor: '#4a4a4a', borderColor: '#5a5a5a', borderWidth: 1 }
                  : { backgroundColor: '#7B68EE' }
              ]}
              onPress={onAccept}
            >
              <Text style={styles.acceptButtonText}>I Accept</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.declineButton,
                darkModeEnabled
                  ? { backgroundColor: '#3a3a3a', borderColor: '#4a4a4a', borderWidth: 1 }
                  : { backgroundColor: '#e0e0e0' }
              ]}
              onPress={onDecline}
            >
              <Text style={[
                styles.declineButtonText,
                { color: darkModeEnabled ? '#e0e0e0' : '#333333' }
              ]}>Not Now</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.privacyLink}
              onPress={onViewPrivacyPolicy}
            >
              <Text style={[
                styles.privacyLinkText,
                { color: darkModeEnabled ? '#8a8a8a' : '#7B68EE' }
              ]}>
                View Privacy Policy
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 15,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  header: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 15,
  },
  headerText: {
    fontSize: 22,
    fontWeight: 'bold',
  },
  iconContainer: {
    marginVertical: 15,
    alignItems: 'center',
  },
  scrollContainer: {
    width: '100%',
    marginBottom: 15,
  },
  infoText: {
    fontSize: 16,
    marginBottom: 15,
    textAlign: 'center',
    lineHeight: 22,
  },
  bulletPoints: {
    width: '100%',
    marginVertical: 10,
    paddingHorizontal: 10,
  },
  bulletPoint: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  bulletText: {
    fontSize: 16,
    marginLeft: 10,
    flex: 1,
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
  },
  acceptButton: {
    width: '100%',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 10,
  },
  acceptButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  declineButton: {
    width: '100%',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 15,
  },
  declineButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  privacyLink: {
    marginTop: 5,
  },
  privacyLinkText: {
    fontSize: 14,
    textDecorationLine: 'underline',
  },
});

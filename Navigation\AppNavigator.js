import React, { useState, useEffect } from 'react';
import { NavigationContainer, DefaultTheme } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { TouchableOpacity, Text, ActivityIndicator, View, Image, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import HomeScreen from '../Screens/HomeScreen';
import ResultScreen from '../Screens/ResultScreen';
import ConnectScreen from '../Screens/ConnectScreen';
import LoginScreen from '../Screens/LoginScreen';
import SignupScreen from '../Screens/SignupScreen';
import SettingsScreen from '../Screens/SettingsScreen';
import UserProfileScreen from '../Screens/UserProfileScreen';
import EditProfileScreen from '../Screens/EditProfileScreen';
import UserGuideScreen from '../Screens/UserGuideScreen';
import MinimalTabBar from '../components/MinimalTabBar';
import NavigationBackground from '../components/NavigationBackground';
import { useAuth } from '../contexts/AuthContext';
import { useSettings } from '../contexts/SettingsContext';
import { navigationRef } from '../utils/navigationRef';



// Create custom themes
const DarkTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    background: '#121212',
    card: '#121212',
    text: '#ffffff',
    border: '#333333',
    // Add these to ensure consistent colors during transitions
    notification: '#121212',
    primary: '#d442f5',
  },
};

const LightTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    background: '#f5f5f5',
    card: '#ffffff',
    text: '#121212',
    border: '#e0e0e0',
    // Add these to ensure consistent colors during transitions
    notification: '#ffffff',
    primary: '#d442f5',
  },
};

// Create navigation stacks and tabs
const AuthStack = createNativeStackNavigator();
const MainStack = createNativeStackNavigator();
const ProfileStack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

// Stack for unauthenticated users
function AuthNavigator() {
  return (
    <AuthStack.Navigator
      screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor: '#121212' },
        animation: 'fade',
        animationDuration: 150,
        presentation: 'transparentModal',
      }}
    >
      <AuthStack.Screen name="Login" component={LoginScreen} />
      <AuthStack.Screen name="Signup" component={SignupScreen} />
    </AuthStack.Navigator>
  );
}

// Tab Navigator for main app screens
function TabNavigator() {
  // Create a simple tab navigator with minimal configuration
  return (
    <Tab.Navigator
      tabBar={props => {
        try {
          return <MinimalTabBar {...props} />;
        } catch (error) {
          console.error('Error rendering tab bar:', error);
          return null; // Return null to use default tab bar as fallback
        }
      }}
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: '#FFFFFF',
        tabBarInactiveTintColor: '#9e9e9e',
      }}
    >
      <Tab.Screen
        name="HomeTab"
        component={HomeScreen}
        options={{
          title: 'Home',
          headerTitle: 'Synaptix'
        }}
      />
      <Tab.Screen
        name="ConnectTab"
        component={ConnectScreen}
        options={{
          title: 'Connect',
        }}
      />
      <Tab.Screen
        name="SettingsTab"
        component={SettingsScreen}
        options={{
          title: 'Settings',
        }}
      />

    </Tab.Navigator>
  );
}

// Profile Stack Navigator
function ProfileNavigator() {
  return (
    <ProfileStack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#121212',
        },
        headerTintColor: '#ffffff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        headerShadowVisible: false,
        contentStyle: { backgroundColor: '#121212' },
        animation: 'fade',
        animationDuration: 150,
        presentation: 'transparentModal',
      }}
    >
      <ProfileStack.Screen
        name="UserProfile"
        component={UserProfileScreen}
        options={{ headerShown: false }}
      />
      <ProfileStack.Screen
        name="EditProfile"
        component={EditProfileScreen}
        options={{ title: 'Edit Profile' }}
      />
    </ProfileStack.Navigator>
  );
}

// Stack for authenticated users
function MainNavigator() {
  return (
    <MainStack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#121212',
        },
        headerTintColor: '#ffffff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        headerShadowVisible: false,
        contentStyle: { backgroundColor: '#121212' },
        animation: 'fade',
        animationDuration: 150,
        presentation: 'transparentModal',
      }}
    >
      <MainStack.Screen
        name="Tabs"
        component={TabNavigator}
        options={{ headerShown: false }}
      />
      <MainStack.Screen
        name="Result"
        component={ResultScreen}
        options={{ title: 'Brain Analysis' }}
      />
      <MainStack.Screen
        name="UserProfile"
        component={ProfileScreen}
        options={{ headerShown: false }}
      />
      <MainStack.Screen
        name="EditProfile"
        component={EditProfileScreen}
        options={{ headerShown: false }}
      />
      <MainStack.Screen
        name="UserGuideTab"
        component={UserGuideScreen}
        options={{ headerShown: false }}
      />
    </MainStack.Navigator>
  );
}

// Styles for the AppNavigator
const styles = {
  centeredContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#121212',
  },
  errorText: {
    color: '#ffffff',
    fontSize: 16,
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#d442f5',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 20,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
  },
};

export default function AppNavigator() {
  // Simple state for auth status
  const [authError, setAuthError] = React.useState(false);

  // Get auth state with error handling
  let currentUser = null;
  let loading = true;

  try {
    // Try to get auth context
    const auth = useAuth();
    currentUser = auth?.currentUser || null;
    loading = auth?.loading || false;
  } catch (err) {
    // Handle auth errors
    console.error('Auth error:', err);
    loading = false;
    setAuthError(true);
  }

  // Get settings using the hook
  const { darkModeEnabled } = useSettings();

  // Show loading screen
  if (loading) {
    return (
      <View style={styles.centeredContainer}>
        <ActivityIndicator size="large" color="#ffffff" />
      </View>
    );
  }

  // Show error screen
  if (authError) {
    return (
      <View style={styles.centeredContainer}>
        <Text style={styles.errorText}>
          Authentication error
        </Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => setAuthError(false)}
        >
          <Text style={styles.buttonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Main navigation
  return (
    <NavigationBackground>
      <NavigationContainer
        ref={navigationRef}
        theme={darkModeEnabled ? DarkTheme : LightTheme}
      >
        {currentUser ? <MainNavigator /> : <AuthNavigator />}
      </NavigationContainer>
    </NavigationBackground>
  );
}

/**
 * API Service for brain data processing
 *
 * This service provides mock data for brain analysis results.
 * It was previously used to communicate with a Flask backend, but now
 * operates in standalone mode with mock data.
 */

/**
 * Get prediction results based on EEG data
 * @param {Object} eegData The EEG data to process
 * @returns {Promise<Object>} Prediction results
 */
export const getPrediction = async (eegData) => {
  try {
    console.log('Processing EEG data:', JSON.stringify({ eegData }));

    // Validate eegData
    if (!eegData) {
      console.error('Invalid eegData: eegData is null or undefined');
      return getMockPrediction();
    }

    if (!eegData.channels || !Array.isArray(eegData.channels)) {
      console.error('Invalid eegData: channels is missing or not an array', eegData);
      // Create a valid channels array if missing
      eegData.channels = [180, 200, 160, 190, 210, 175, 195, 185]; // Mock channel values
    }

    // Add timestamp if missing
    if (!eegData.timestamp) {
      eegData.timestamp = Date.now();
    }

    // For now, we're just returning mock data
    // In the future, this could be replaced with local processing logic
    console.log('Returning mock prediction results');
    return getMockPrediction();
  } catch (error) {
    console.error('Prediction error:', error);
    console.log('Error occurred, falling back to mock data');
    return getMockPrediction();
  }
};

/**
 * Get mock prediction results
 * @returns {Object} Mock prediction results
 */
export const getMockPrediction = () => {
  return {
    overallScore: 92,
    categories: [
      { name: 'Cognitive Function', score: 95, color: '#ffffff' },
      { name: 'Memory', score: 88, color: '#7986cb' },
      { name: 'Processing Speed', score: 94, color: '#4db6ac' },
      { name: 'Attention', score: 91, color: '#ffb74d' },
      { name: 'Alpha Waves', score: 89, color: '#ff8a65' },
      { name: 'Beta Waves', score: 93, color: '#ba68c8' },
      { name: 'Theta Waves', score: 87, color: '#4fc3f7' },
      { name: 'Delta Waves', score: 90, color: '#81c784' },
    ],
    recommendations: [
      'Regular mental exercises can help maintain cognitive function',
      'Consider memory enhancement techniques for optimal performance',
      'Your processing speed is excellent, continue challenging yourself',
      'Mindfulness practices may help improve attention further'
    ]
  };
};

/**
 * Mock health check function
 * @returns {Promise<Object>} Health status
 */
export const checkApiHealth = async () => {
  console.log('Performing mock health check');
  return {
    status: 'healthy',
    timestamp: Date.now(),
    server: 'Mock API',
    version: '1.0.0'
  };
};

export default {
  checkApiHealth,
  getPrediction,
  getMockPrediction,
};

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import { Platform, Share } from 'react-native';

/**
 * Get all keys stored in AsyncStorage
 * @returns {Promise<string[]>} Array of keys
 */
export const getAllStorageKeys = async () => {
  try {
    return await AsyncStorage.getAllKeys();
  } catch (error) {
    console.error('Error getting all storage keys:', error);
    return [];
  }
};

/**
 * Get all data from AsyncStorage
 * @returns {Promise<Object>} Object with all data
 */
export const getAllStorageData = async () => {
  try {
    const keys = await getAllStorageKeys();
    const result = {};
    
    // Get all items
    for (const key of keys) {
      // Skip Supabase auth tokens for security
      if (key.startsWith('sb-')) continue;
      
      const value = await AsyncStorage.getItem(key);
      if (value !== null) {
        try {
          // Try to parse JSON values
          result[key] = JSON.parse(value);
        } catch {
          // If not JSON, store as string
          result[key] = value;
        }
      }
    }
    
    return result;
  } catch (error) {
    console.error('Error getting all storage data:', error);
    return {};
  }
};

/**
 * Create a backup of all AsyncStorage data
 * @returns {Promise<string|null>} Path to the backup file or null if failed
 */
export const createBackup = async () => {
  try {
    // Get all data
    const data = await getAllStorageData();
    
    // Create backup object with metadata
    const backup = {
      version: 1,
      timestamp: new Date().toISOString(),
      platform: Platform.OS,
      data,
    };
    
    // Convert to JSON string
    const backupString = JSON.stringify(backup, null, 2);
    
    // Create filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `synaptix-backup-${timestamp}.json`;
    
    // Save to file
    if (Platform.OS === 'web') {
      // For web, we can't use FileSystem, so we'll return the data directly
      return backupString;
    } else {
      // For native platforms, save to file
      const backupDir = `${FileSystem.documentDirectory}backups/`;
      
      // Ensure directory exists
      const dirInfo = await FileSystem.getInfoAsync(backupDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(backupDir, { intermediates: true });
      }
      
      const filePath = `${backupDir}${filename}`;
      await FileSystem.writeAsStringAsync(filePath, backupString);
      
      return filePath;
    }
  } catch (error) {
    console.error('Error creating backup:', error);
    return null;
  }
};

/**
 * Share the backup file
 * @param {string} backupPath Path to the backup file
 * @returns {Promise<boolean>} Whether sharing was successful
 */
export const shareBackup = async (backupPath) => {
  try {
    if (Platform.OS === 'web') {
      // For web, we can't use Share API, so we'll just log
      console.log('Backup data:', backupPath);
      return true;
    } else {
      // For native platforms, use Share API
      const result = await Share.share({
        title: 'Synaptix Backup',
        message: 'Here is your Synaptix data backup',
        url: backupPath,
      });
      
      return result.action !== Share.dismissedAction;
    }
  } catch (error) {
    console.error('Error sharing backup:', error);
    return false;
  }
};

/**
 * Perform a data backup
 * @returns {Promise<boolean>} Whether backup was successful
 */
export const performBackup = async () => {
  try {
    const backupPath = await createBackup();
    if (!backupPath) {
      return false;
    }
    
    // For now, we'll just create the backup but not share it automatically
    console.log('Backup created at:', backupPath);
    return true;
  } catch (error) {
    console.error('Error performing backup:', error);
    return false;
  }
};

export default {
  getAllStorageKeys,
  getAllStorageData,
  createBackup,
  shareBackup,
  performBackup,
};

# PowerShell script to fix permissions for EAS builds
# This script sets read and execute permissions for all files in the project directory
# and performs additional checks to ensure your project is ready for EAS build

Write-Host "========== Synaptix EAS Build Preparation ==========" -ForegroundColor Cyan
Write-Host "Setting permissions for EAS build..." -ForegroundColor Green

# Check if running as administrator
$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
$isAdmin = $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "WARNING: This script is not running as Administrator. Some permission changes may fail." -ForegroundColor Yellow
    Write-Host "Consider rerunning as Administrator if you encounter permission errors." -ForegroundColor Yellow
    Write-Host ""
}

# Get the current directory
$projectDir = Get-Location

# Check for .git directory to ensure we're in the project root
if (-not (Test-Path ".git")) {
    Write-Host "WARNING: .git directory not found. Make sure you're running this script from the project root." -ForegroundColor Yellow
}

# Check for node_modules
if (-not (Test-Path "node_modules")) {
    Write-Host "WARNING: node_modules directory not found. Run 'npm install' before building." -ForegroundColor Yellow
}

# Set read permissions for all files recursively
Write-Host "Setting read permissions for all files..." -ForegroundColor Yellow
$errorCount = 0
Get-ChildItem -Path $projectDir -Recurse -Force | ForEach-Object {
    # Skip node_modules to avoid unnecessary permission changes
    if ($_.FullName -notlike "*\node_modules\*" -and $_.FullName -notlike "*\.git\*") {
        try {
            $acl = Get-Acl $_.FullName
            $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("Everyone", "ReadAndExecute", "Allow")
            $acl.SetAccessRule($accessRule)
            Set-Acl $_.FullName $acl
        }
        catch {
            $errorCount++
            Write-Host "Could not set permissions for $($_.FullName): $_" -ForegroundColor Red
        }
    }
}

# Ensure app.json exists (copy from app.json.bak if needed)
if (-not (Test-Path "app.json") -and (Test-Path "app.json.bak")) {
    Write-Host "Creating app.json from app.json.bak..." -ForegroundColor Yellow
    Copy-Item -Path "app.json.bak" -Destination "app.json"
}

# Check for essential files
$essentialFiles = @("app.json", "eas.json", "package.json")
foreach ($file in $essentialFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "ERROR: Essential file $file not found!" -ForegroundColor Red
    }
}

# Check if user is logged in to EAS
Write-Host "Checking EAS login status..." -ForegroundColor Yellow
$easWhoami = npx eas whoami 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "WARNING: You are not logged in to EAS. Run 'npx eas login' before building." -ForegroundColor Yellow
}
else {
    Write-Host "You are logged in to EAS as: $easWhoami" -ForegroundColor Green
}

# Check for .env file and warn if not found
if (-not (Test-Path ".env")) {
    Write-Host "NOTE: No .env file found. If your app requires environment variables, create a .env file." -ForegroundColor Yellow
}

# Summary
Write-Host ""
Write-Host "========== Preparation Complete ==========" -ForegroundColor Cyan
if ($errorCount -gt 0) {
    Write-Host "Completed with $errorCount permission errors. Some files may not have proper permissions." -ForegroundColor Yellow
}
else {
    Write-Host "Permissions have been set successfully!" -ForegroundColor Green
}

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Run 'npx eas build --platform android --profile development' to build for Android" -ForegroundColor White
Write-Host "2. Run 'npx eas build --platform ios --profile development' to build for iOS" -ForegroundColor White
Write-Host "3. If you encounter any issues, check README_PERMISSIONS_FIX.md for troubleshooting" -ForegroundColor White
Write-Host ""

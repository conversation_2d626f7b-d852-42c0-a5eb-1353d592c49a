/**
 * Utility functions for form validation
 */

/**
 * Validates an email address
 * @param {string} email - The email address to validate
 * @returns {boolean} - True if the email is valid, false otherwise
 */
export const validateEmail = (email) => {
  if (!email || email.trim() === '') return false;

  // More comprehensive email regex that checks for valid TLDs
  // This will reject emails with typos like .cpm instead of .com
  const emailRegex = /^[^\s@]+@[^\s@]+\.(com|net|org|edu|gov|mil|co|io|ai|me|info|biz|[a-z]{2})$/i;
  return emailRegex.test(email);
};

/**
 * Validates a name
 * @param {string} name - The name to validate
 * @returns {boolean} - True if the name is valid, false otherwise
 */
export const validateName = (name) => {
  return name && name.trim().length >= 2;
};

/**
 * Validates a date of birth
 * @param {string} dob - The date of birth to validate
 * @returns {boolean} - True if the date is valid, false otherwise
 */
export const validateDOB = (dob) => {
  if (!dob || !dob.trim()) return false;

  // Check if it's in MM/DD/YYYY format - more flexible regex
  // Allows single or double digits for month and day
  const dateRegex = /^([1-9]|0[1-9]|1[0-2])\/([1-9]|0[1-9]|[12][0-9]|3[01])\/\d{4}$/;
  if (!dateRegex.test(dob)) return false;

  // Check if it's a valid date
  const parts = dob.split('/');
  const month = parseInt(parts[0], 10);
  const day = parseInt(parts[1], 10);
  const year = parseInt(parts[2], 10);

  // Create date object and check if the date is valid
  const date = new Date(year, month - 1, day);

  // Check if the date is valid by comparing the components
  // This handles cases like February 30 which would be converted to March 2
  const isValidDate =
    date.getFullYear() === year &&
    date.getMonth() === month - 1 &&
    date.getDate() === day;

  // Check if the year is in a reasonable range
  const isValidYear = year >= 1920 && year <= new Date().getFullYear();

  return isValidDate && isValidYear;
};

/**
 * Validates a password
 * @param {string} password - The password to validate
 * @returns {object} - Object with isValid boolean and message string
 */
export const validatePassword = (password) => {
  if (!password) {
    return { isValid: false, message: 'Password is required' };
  }

  if (password.length < 6) {
    return { isValid: false, message: 'Password must be at least 6 characters' };
  }

  // Optional: Add more password requirements like uppercase, lowercase, numbers, etc.
  // const hasUpperCase = /[A-Z]/.test(password);
  // const hasLowerCase = /[a-z]/.test(password);
  // const hasNumbers = /\d/.test(password);
  // const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  // if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
  //   return {
  //     isValid: false,
  //     message: 'Password must contain uppercase, lowercase, numbers, and special characters'
  //   };
  // }

  return { isValid: true, message: '' };
};

/**
 * Validates a phone number
 * @param {string} phone - The phone number to validate
 * @returns {boolean} - True if the phone number is valid, false otherwise
 */
export const validatePhone = (phone) => {
  if (!phone || phone.trim() === '') return true; // Optional field

  // Basic phone validation - can be adjusted based on your requirements
  const phoneRegex = /^\+?[0-9]{10,15}$/;
  return phoneRegex.test(phone.replace(/[\s-()]/g, ''));
};

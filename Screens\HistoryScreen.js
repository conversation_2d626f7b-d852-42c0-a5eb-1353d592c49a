import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import CustomHeader from '../components/CustomHeader';
import { ensureStatusBarAppearance } from '../utils/screenTransition';
import { useSettings } from '../contexts/SettingsContext';

export default function HistoryScreen() {
  const navigation = useNavigation();
  const { darkModeEnabled } = useSettings();

  // Sample history data - in a real app, this would come from a database or API
  const historyData = [
    {
      id: '1',
      date: 'Today, 10:30 AM',
      title: 'EEG Analysis',
      duration: '15 minutes',
      waveType: 'Alpha/Beta',
      result: 'Normal',
      abnormalityScore: 12, // Lower score means more normal
    },
    {
      id: '2',
      date: 'Yesterday, 2:15 PM',
      title: 'Brainwave Scan',
      duration: '10 minutes',
      waveType: 'Theta/Delta',
      result: 'Normal',
      abnormalityScore: 15,
    },
    {
      id: '3',
      date: 'May 15, 2023, 9:00 AM',
      title: 'Neural Pattern Test',
      duration: '20 minutes',
      waveType: 'Full Spectrum',
      result: 'Normal',
      abnormalityScore: 10,
    },
    {
      id: '4',
      date: 'May 12, 2023, 11:45 AM',
      title: 'EEG Monitoring',
      duration: '12 minutes',
      waveType: 'Beta/Gamma',
      result: 'Normal',
      abnormalityScore: 8,
    },
    {
      id: '5',
      date: 'May 10, 2023, 4:30 PM',
      title: 'Baseline EEG',
      duration: '15 minutes',
      waveType: 'Alpha/Theta',
      result: 'Normal',
      abnormalityScore: 5,
    },
  ];

  // Ensure StatusBar is properly configured when the screen mounts
  useEffect(() => {
    ensureStatusBarAppearance(darkModeEnabled);
  }, [darkModeEnabled]);

  const handleBackPress = () => {
    navigation.goBack();
  };

  // Render each history item
  const renderHistoryItem = ({ item }) => {
    // All results are normal, so we use green
    const resultColor = '#4CAF50'; // Green for normal

    // All icons are checkmarks for normal
    const resultIcon = 'checkmark-circle-outline';

    return (
      <TouchableOpacity
        style={[
          styles.historyItem,
          { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
        ]}
        onPress={() => {
          // Navigate to details screen or show details modal
          // For now, we'll just log the item
          console.log('History item pressed:', item);
        }}
      >
        <View style={styles.historyContent}>
          <Text style={[
            styles.historyDate,
            { color: darkModeEnabled ? '#9e9e9e' : '#757575' }
          ]}>
            {item.date}
          </Text>
          <View style={styles.titleContainer}>
            <Text style={[
              styles.historyTitle,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              {item.title}
            </Text>
            <View style={[styles.resultBadge, { backgroundColor: resultColor }]}>
              <Text style={styles.resultText}>{item.result}</Text>
            </View>
          </View>
          <View style={styles.historyDetails}>
            <Text style={[
              styles.historyDetail,
              { color: darkModeEnabled ? '#cccccc' : '#666666' }
            ]}>
              <Ionicons name="time-outline" size={14} /> {item.duration}
            </Text>
            <Text style={[
              styles.historyDetail,
              { color: darkModeEnabled ? '#cccccc' : '#666666' }
            ]}>
              <Ionicons name="pulse-outline" size={14} /> {item.waveType}
            </Text>
          </View>
        </View>
        <View style={styles.resultIconContainer}>
          <Ionicons
            name={resultIcon}
            size={24}
            color={resultColor}
          />
          <Ionicons
            name="chevron-forward"
            size={20}
            color={darkModeEnabled ? "#666666" : "#999999"}
            style={styles.arrowIcon}
          />
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[
      styles.container,
      { backgroundColor: darkModeEnabled ? '#121212' : '#f5f5f5' }
    ]}>
      <StatusBar barStyle={darkModeEnabled ? "light-content" : "dark-content"} />
      <CustomHeader
        title="History"
        showProfileIcon={false}
        showBackButton={true}
        onBackPress={handleBackPress} />

      <View style={styles.summaryContainer}>
        <View style={[
          styles.summaryCard,
          {
            backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff',
            borderLeftWidth: 4,
            borderLeftColor: '#9C27B0', // Purple to match EEG theme
          }
        ]}>
          <Text style={[
            styles.summaryTitle,
            { color: darkModeEnabled ? '#ffffff' : '#121212' }
          ]}>
            EEG Analysis Summary
          </Text>
          <View style={styles.summaryStats}>
            <View style={styles.statItem}>
              <Text style={[
                styles.statValue,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>5</Text>
              <Text style={[
                styles.statLabel,
                { color: darkModeEnabled ? '#9e9e9e' : '#757575' }
              ]}>Total Scans</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[
                styles.statValue,
                { color: '#4CAF50' } // Green for normal
              ]}>5</Text>
              <Text style={[
                styles.statLabel,
                { color: darkModeEnabled ? '#9e9e9e' : '#757575' }
              ]}>Normal</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[
                styles.statValue,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>10.0</Text>
              <Text style={[
                styles.statLabel,
                { color: darkModeEnabled ? '#9e9e9e' : '#757575' }
              ]}>Avg Score</Text>
            </View>
          </View>
        </View>
      </View>

      <View style={styles.historyListContainer}>
        <Text style={[
          styles.historyListTitle,
          { color: darkModeEnabled ? '#ffffff' : '#121212' }
        ]}>
          EEG Scan History
        </Text>
        <FlatList
          data={historyData}
          renderItem={renderHistoryItem}
          keyExtractor={item => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.historyList}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  summaryContainer: {
    padding: 16,
    paddingBottom: 0,
    marginBottom: 10,
  },
  summaryCard: {
    borderRadius: 10,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
  },
  historyListContainer: {
    flex: 1,
    padding: 16,
    paddingTop: 0,
  },
  historyListTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  historyList: {
    paddingBottom: 20,
  },
  historyItem: {
    flexDirection: 'row',
    borderRadius: 10,
    marginBottom: 10,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  historyContent: {
    flex: 1,
  },
  historyDate: {
    fontSize: 12,
    marginBottom: 4,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  resultBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  resultText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  historyDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  historyDetail: {
    fontSize: 14,
  },
  resultIconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  arrowIcon: {
    marginTop: 8,
  },
});

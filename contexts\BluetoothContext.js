import React, { createContext, useState, useContext, useEffect, useRef } from 'react';
import { Alert, Platform, Linking, AppState } from 'react-native';
import bluetoothHelper from '../utils/bluetoothHelper';
import apiService from '../utils/apiService';

// Create the Bluetooth context
const BluetoothContext = createContext();

// Custom hook to use the Bluetooth context
export const useBluetooth = () => {
  return useContext(BluetoothContext);
};

// Provider component for the Bluetooth context
export const BluetoothProvider = ({ children }) => {
  // State variables
  const [isBluetoothEnabled, setIsBluetoothEnabled] = useState(false);
  const [permissionsGranted, setPermissionsGranted] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [devices, setDevices] = useState([]);
  const [connectedDevice, setConnectedDevice] = useState(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [eegData, setEegData] = useState(null);
  const [deviceStatus, setDeviceStatus] = useState('disconnected'); // 'disconnected', 'connected', 'streaming'
  const [permissionDeniedPermanently, setPermissionDeniedPermanently] = useState(false);

  // Reference to track if we've shown the Bluetooth enable dialog
  const bluetoothAlertShown = useRef(false);
  // Reference to track if we've shown the permissions dialog
  const permissionsAlertShown = useRef(false);
  // Reference to track the status check interval
  const statusCheckInterval = useRef(null);
  // Reference to the AppState subscription
  const appStateSubscription = useRef(null);
  // Error throttling to prevent spam
  const lastErrorTime = useRef(0);
  const lastPermissionCheck = useRef(0);
  const errorThrottleMs = 5000; // Throttle errors to once every 5 seconds
  const permissionCheckThrottleMs = 10000; // Check permissions only once every 10 seconds

  // Helper function to log errors with throttling
  const logThrottledError = (message, error) => {
    const now = Date.now();
    if (now - lastErrorTime.current > errorThrottleMs) {
      console.error(message, error);
      lastErrorTime.current = now;
    }
  };

  // Helper function to check if permission check should be throttled
  const shouldThrottlePermissionCheck = () => {
    const now = Date.now();
    if (now - lastPermissionCheck.current > permissionCheckThrottleMs) {
      lastPermissionCheck.current = now;
      return false;
    }
    return true;
  };

  // Function to check Bluetooth status
  const checkBluetoothStatus = async () => {
    try {
      // Get the BLE manager - wrap in try/catch to handle potential initialization issues
      let manager;
      let state;

      try {
        manager = bluetoothHelper.getBleManager();

        // Add a small delay to ensure manager is fully initialized
        await new Promise(resolve => setTimeout(resolve, 100));

        // Check if the manager is valid before calling state()
        if (!manager || typeof manager.state !== 'function') {
          throw new Error('BLE manager is not properly initialized');
        }

        state = await manager.state();
      } catch (bleError) {
        logThrottledError('❌ Error getting BLE manager or state:', bleError);

        // If we get a "BleManager was destroyed" error, try to recreate it
        if (bleError.message && bleError.message.includes('destroyed')) {
          console.log('🔄 BLE Manager was destroyed, attempting to recreate...');
          try {
            // Force cleanup and recreate
            bluetoothHelper.cleanupBluetooth();
            await new Promise(resolve => setTimeout(resolve, 500)); // Wait a bit
            manager = bluetoothHelper.getBleManager();
            state = await manager.state();
          } catch (retryError) {
            logThrottledError('❌ Failed to recreate BLE manager:', retryError);
            // Set mock mode if BLE manager fails completely
            setIsBluetoothEnabled(false);
            setPermissionsGranted(false);
            return;
          }
        } else {
          // Set mock mode if BLE manager fails
          setIsBluetoothEnabled(false);
          setPermissionsGranted(false);
          return;
        }
      }

      // Check if BLE is supported by checking the state
      if (state === 'Unsupported') {
        console.log('ℹ️ BLE not supported on this device, using mock mode');
        setIsBluetoothEnabled(true); // Set to true for mock mode
        setPermissionsGranted(true);
        return;
      }

      const isEnabled = state === 'PoweredOn';

      // Only update state if there's a change to avoid unnecessary re-renders
      if (isEnabled !== isBluetoothEnabled) {
        setIsBluetoothEnabled(isEnabled);

        // If Bluetooth was just enabled and we have permissions, start scanning
        if (isEnabled && permissionsGranted && !isScanning && devices.length === 0) {
          startScan();
        }

        // If Bluetooth was just disabled, clear devices list
        if (!isEnabled && devices.length > 0) {
          setDevices([]);
        }

        // Show alert if Bluetooth is disabled and we haven't shown it yet
        if (!isEnabled && !bluetoothAlertShown.current) {
          bluetoothAlertShown.current = true;

          if (Platform.OS === 'android') {
            Alert.alert(
              'Bluetooth is Turned Off',
              'Please enable Bluetooth in your device settings to connect to your EEG device.',
              [
                {
                  text: 'Open Settings',
                  onPress: () => {
                    // On Android, we can open the Bluetooth settings
                    Linking.openSettings();
                  }
                },
                {
                  text: 'Cancel',
                  style: 'cancel',
                  onPress: () => {
                    // Reset the flag after a delay so we can show the alert again if needed
                    setTimeout(() => {
                      bluetoothAlertShown.current = false;
                    }, 5000);
                  }
                }
              ]
            );
          }
        }
      }

      // Check permissions status (with throttling)
      if (!shouldThrottlePermissionCheck()) {
        const permissionsStatus = await bluetoothHelper.checkBluetoothPermissions();

        // Only update if there's a change
        if (permissionsStatus !== permissionsGranted) {
          setPermissionsGranted(permissionsStatus);

          // If permissions were just granted and Bluetooth is enabled, start scanning
          if (permissionsStatus && isEnabled && !isScanning && devices.length === 0) {
            startScan();
          }
        }

        // If permissions are not granted and we haven't shown the alert yet
        if (!permissionsStatus && !permissionDeniedPermanently && !permissionsAlertShown.current) {
        permissionsAlertShown.current = true;

        // Request permissions
        const granted = await bluetoothHelper.requestBluetoothPermissions();
        setPermissionsGranted(granted);

        // If still not granted after request, show alert with option to open settings
        if (!granted) {
          Alert.alert(
            'Bluetooth Permissions Required',
            'Synaptix needs Bluetooth permissions to connect to your EEG device. Please grant these permissions in your device settings.',
            [
              {
                text: 'Open Settings',
                onPress: () => {
                  Linking.openSettings();
                }
              },
              {
                text: 'Cancel',
                style: 'cancel',
                onPress: () => {
                  // Mark as permanently denied if user cancels
                  setPermissionDeniedPermanently(true);
                }
              }
            ]
          );
        }

          // Reset the flag after a delay
          setTimeout(() => {
            permissionsAlertShown.current = false;
          }, 5000);
        }
      } // End of throttling check
    } catch (error) {
      logThrottledError('❌ Error checking Bluetooth status:', error);
    }
  };

  // Set up periodic Bluetooth status check and app state listener
  useEffect(() => {
    // Check status immediately
    checkBluetoothStatus();

    // Set up interval to check status periodically
    statusCheckInterval.current = setInterval(checkBluetoothStatus, 3000);

    // Set up app state listener to check status when app comes to foreground
    appStateSubscription.current = AppState.addEventListener('change', (nextAppState) => {
      if (nextAppState === 'active') {
        // Reset alert flags when app comes to foreground
        bluetoothAlertShown.current = false;
        permissionsAlertShown.current = false;

        // Check status
        checkBluetoothStatus();
      }
    });

    // Clean up Bluetooth resources when component unmounts
    return () => {
      try {
        console.log('🧹 BluetoothContext cleanup starting...');

        // Clear interval first
        if (statusCheckInterval.current) {
          clearInterval(statusCheckInterval.current);
          statusCheckInterval.current = null;
        }

        // Remove app state listener
        if (appStateSubscription.current) {
          appStateSubscription.current.remove();
          appStateSubscription.current = null;
        }

        // Clean up Bluetooth operations
        try {
          if (isScanning) {
            stopScan();
          }
        } catch (scanError) {
          console.error('Error stopping scan during cleanup:', scanError);
        }

        try {
          if (connectedDevice) {
            disconnectFromDevice();
          }
        } catch (disconnectError) {
          console.error('Error disconnecting device during cleanup:', disconnectError);
        }

        // Only cleanup Bluetooth if we're actually unmounting (not just re-rendering)
        // Add a small delay to ensure other operations complete first
        setTimeout(() => {
          try {
            bluetoothHelper.cleanupBluetooth();
          } catch (cleanupError) {
            console.error('Error cleaning up Bluetooth during unmount:', cleanupError);
          }
        }, 100);

        console.log('✅ BluetoothContext cleanup completed');
      } catch (error) {
        console.error('Error during component cleanup:', error);
      }
    };
  }, [isBluetoothEnabled, permissionsGranted, isScanning, devices.length]);

  // Start scanning for devices
  const startScan = () => {
    // Check if Bluetooth is enabled and permissions are granted
    if (!permissionsGranted || !isBluetoothEnabled) {
      // If Bluetooth is not enabled, show a more specific message
      if (!isBluetoothEnabled) {
        Alert.alert(
          'Bluetooth is Turned Off',
          'Please enable Bluetooth in your device settings to scan for devices.',
          [
            {
              text: 'Open Settings',
              onPress: () => Linking.openSettings()
            },
            { text: 'Cancel', style: 'cancel' }
          ]
        );
      }
      // If permissions are not granted, show a permissions-specific message
      else if (!permissionsGranted) {
        Alert.alert(
          'Bluetooth Permissions Required',
          'Synaptix needs Bluetooth permissions to scan for devices. Please grant these permissions.',
          [
            {
              text: 'Request Permissions',
              onPress: async () => {
                const granted = await bluetoothHelper.requestBluetoothPermissions();
                setPermissionsGranted(granted);
                if (granted) {
                  // If permissions were just granted, start scanning
                  startScan();
                } else {
                  // If still not granted, offer to open settings
                  Alert.alert(
                    'Permissions Denied',
                    'Please enable Bluetooth permissions in your device settings.',
                    [
                      { text: 'Open Settings', onPress: () => Linking.openSettings() },
                      { text: 'Cancel', style: 'cancel' }
                    ]
                  );
                }
              }
            },
            { text: 'Cancel', style: 'cancel' }
          ]
        );
      }
      return;
    }

    // Clear previous devices and set scanning state
    setIsScanning(true);
    setDevices([]);

    // Start scanning and get the stop function
    let stopScanFunction;
    try {
      stopScanFunction = bluetoothHelper.startScan(
        (device) => {
          // Check if the device is an Arduino Nano 33 BLE or other EEG device
          const isArduino = bluetoothHelper.isArduinoNano33BLE(device);
          const isEEG = bluetoothHelper.isEEGDevice(device);

        // Add device to list if it's not already there
        setDevices(prevDevices => {
          // Check if device already exists in the list
          const deviceExists = prevDevices.some(d => d.id === device.id);

          if (!deviceExists) {
            // Add a property to indicate if it's an Arduino or EEG device
            const enhancedDevice = {
              ...device,
              isArduino,
              isEEG
            };

            // Add the device to the list
            return [...prevDevices, enhancedDevice];
          }

          return prevDevices;
        });
      },
      (error) => {
        console.error('Bluetooth scan error:', error);
        setIsScanning(false);

        // Show a more specific error message based on the error
        let errorMessage = 'An error occurred while scanning for devices.';

        if (error.message && error.message.includes('bluetooth')) {
          errorMessage = 'Bluetooth error: ' + error.message;
        } else if (error.message && error.message.includes('permission')) {
          errorMessage = 'Permission error: ' + error.message;
        }

        Alert.alert(
          'Scan Error',
          `${errorMessage} Please try again.`,
          [
            {
              text: 'Retry',
              onPress: () => {
                // Wait a moment before retrying
                setTimeout(() => startScan(), 1000);
              }
            },
            { text: 'Cancel', style: 'cancel' }
          ]
        );
      }
    );

    // Stop scanning after 15 seconds (increased from 10 to give more time to find devices)
    setTimeout(() => {
      if (stopScanFunction) {
        try {
          stopScanFunction();
        } catch (error) {
          console.error('Error stopping scan:', error);
        }
      }
      setIsScanning(false);

      // If no devices were found, show a message
      if (devices.length === 0) {
        Alert.alert(
          'No Devices Found',
          'No Bluetooth devices were found. Make sure your EEG device is turned on and in range.',
          [
            { text: 'Scan Again', onPress: () => startScan() },
            { text: 'Cancel', style: 'cancel' }
          ]
        );
      }
    }, 15000);
    } catch (error) {
      console.error('Error starting scan:', error);
      setIsScanning(false);
      Alert.alert(
        'Bluetooth Error',
        'There was an error starting the Bluetooth scan. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  // Stop scanning for devices
  const stopScan = () => {
    try {
      // Get the BLE manager - wrap in try/catch to handle potential initialization issues
      let manager;
      try {
        manager = bluetoothHelper.getBleManager();

        // Check if manager is valid and has the stopDeviceScan method
        if (manager && typeof manager.stopDeviceScan === 'function') {
          manager.stopDeviceScan();
          console.log('✅ Device scan stopped');
        } else {
          console.warn('⚠️ BLE manager not available or invalid for stopping scan');
        }
      } catch (bleError) {
        // Don't log destroyed manager errors as they're expected during cleanup
        if (!bleError.message || !bleError.message.includes('destroyed')) {
          console.error('Error getting BLE manager or stopping scan:', bleError);
        }
      }
      setIsScanning(false);
    } catch (error) {
      console.error('Stop scan error:', error);
      setIsScanning(false);
    }
  };

  // Connect to a device
  const connectToDevice = async (device) => {
    if (!device) return;

    try {
      setIsConnecting(true);

      // Stop scanning while connecting
      try {
        stopScan();
      } catch (scanError) {
        console.error('Error stopping scan before connecting:', scanError);
        // Continue anyway
      }

      // Connect to the device
      try {
        await bluetoothHelper.connectToDevice(
          device,
          (connectedDevice) => {
            console.log('Connected to device:', connectedDevice.name);
            setConnectedDevice(connectedDevice);
            setDeviceStatus('connected');
            setIsConnecting(false);

            // Start listening for EEG data
            try {
              startEEGDataSubscription(connectedDevice);
            } catch (subscriptionError) {
              console.error('Error starting EEG data subscription:', subscriptionError);
              // Continue anyway, we're still connected
            }
          },
          (disconnectedDevice) => {
            console.log('Disconnected from device:', disconnectedDevice?.name);
            setConnectedDevice(null);
            setDeviceStatus('disconnected');
            setEegData(null);
          },
          (error) => {
            console.error('Connection error:', error);
            setIsConnecting(false);
            setDeviceStatus('disconnected');
            Alert.alert(
              'Connection Error',
              'Failed to connect to the device. Please try again.',
              [{ text: 'OK' }]
            );
          }
        );
      } catch (connectError) {
        console.error('Error connecting to device:', connectError);
        setIsConnecting(false);
        setDeviceStatus('disconnected');
        Alert.alert(
          'Connection Error',
          'Failed to connect to the device. Please try again.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Connection error:', error);
      setIsConnecting(false);
      setDeviceStatus('disconnected');
      Alert.alert(
        'Connection Error',
        'Failed to connect to the device. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  // Disconnect from the current device
  const disconnectFromDevice = async () => {
    if (!connectedDevice) return;

    try {
      try {
        await bluetoothHelper.disconnectDevice(connectedDevice);
      } catch (disconnectError) {
        console.error('Error disconnecting device:', disconnectError);
        // Continue anyway to clean up the state
      }

      // Clean up state even if the disconnect fails
      setConnectedDevice(null);
      setDeviceStatus('disconnected');
      setEegData(null);
    } catch (error) {
      console.error('Disconnect error:', error);
      // Still try to clean up the state
      setConnectedDevice(null);
      setDeviceStatus('disconnected');
      setEegData(null);
    }
  };

  // Start listening for EEG data from the device
  const startEEGDataSubscription = async (device) => {
    if (!device) {
      console.error('Cannot subscribe to EEG data: device is null');
      return;
    }

    try {
      // Subscribe to EEG data characteristic
      await bluetoothHelper.subscribeToEEGData(
        device,
        async (data) => {
          // Set the EEG data in state
          setEegData(data);
          setDeviceStatus('streaming');

          // Send the EEG data to the Flask API for processing
          try {
            // Only send data to API if we have valid channel data
            if (data && data.channels && data.channels.length > 0) {
              // Send data to API in the background
              // We don't await this to avoid blocking the UI
              apiService.getPrediction(data)
                .then(result => {
                  console.log('API prediction received:', result);
                })
                .catch(apiError => {
                  console.error('API prediction error:', apiError);
                });
            }
          } catch (apiError) {
            console.error('Error sending data to API:', apiError);
            // We don't show an alert here to avoid interrupting the user experience
          }
        },
        (error) => {
          console.error('EEG data subscription error:', error);
          Alert.alert(
            'Data Error',
            'Failed to receive data from the device.',
            [{ text: 'OK' }]
          );
        }
      );
    } catch (error) {
      console.error('EEG data subscription error:', error);
      // Don't show an alert here as it might be called during initialization
    }
  };

  // Value object to be provided by the context
  const value = {
    isBluetoothEnabled,
    permissionsGranted,
    isScanning,
    devices,
    connectedDevice,
    isConnecting,
    eegData,
    deviceStatus,
    startScan,
    stopScan,
    connectToDevice,
    disconnectFromDevice,
  };

  return (
    <BluetoothContext.Provider value={value}>
      {children}
    </BluetoothContext.Provider>
  );
};

export default BluetoothContext;

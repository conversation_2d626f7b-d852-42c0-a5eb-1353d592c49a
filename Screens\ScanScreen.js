import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  Image,
  ActivityIndicator,
} from 'react-native';
import GradientButton from '../components/GradientButton';

export default function ScanScreen({ navigation }) {
  const [scanning, setScanning] = useState(false);

  const handleStartScan = () => {
    setScanning(true);

    // Simulate scanning process
    setTimeout(() => {
      setScanning(false);
      // Navigate to the Result screen in the main stack
      navigation.navigate('Result');
    }, 3000);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />

      <View style={styles.header}>
        <Text style={styles.title}>Brain Scanner</Text>
        <Text style={styles.subtitle}>Analyze your neural patterns</Text>
      </View>

      <View style={styles.imageContainer}>
        <Image
          source={require('../assets/brain.png')}
          style={styles.brainImage}
          resizeMode="contain"
        />
      </View>

      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>
          Place your device on a flat surface and stay still during the scanning process.
          The scan will analyze your brain's neural patterns and provide detailed insights.
        </Text>
      </View>

      {scanning ? (
        <View style={styles.scanningContainer}>
          <ActivityIndicator size="large" color="#d442f5" />
          <Text style={styles.scanningText}>Scanning in progress...</Text>
          <Text style={styles.scanningSubtext}>Please remain still</Text>
        </View>
      ) : (
        <GradientButton
          text="START BRAIN SCAN"
          onPress={handleStartScan}
          style={styles.scanButton}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
    padding: 20,
  },
  header: {
    marginTop: 50,
    alignItems: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: '#ffffff',
    marginBottom: 20,
  },
  imageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 30,
  },
  brainImage: {
    width: 200,
    height: 200,
  },
  infoContainer: {
    backgroundColor: '#1e1e1e',
    borderRadius: 10,
    padding: 20,
    marginBottom: 30,
  },
  infoText: {
    color: '#e0e0e0',
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
  },
  scanButton: {
    marginBottom: 20,
  },
  scanningContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
  },
  scanningText: {
    color: '#d442f5',
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 15,
  },
  scanningSubtext: {
    color: '#e0e0e0',
    fontSize: 14,
    marginTop: 5,
  },
});

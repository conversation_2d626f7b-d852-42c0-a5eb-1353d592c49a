#!/bin/bash
# Make this script executable with: chmod +x run_server.sh

echo "Starting Synaptix Flask API Server..."
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is not installed."
    echo "Please install Python from https://www.python.org/downloads/"
    exit 1
fi

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "pip is not installed."
    echo "Please install pip or reinstall Python with pip included."
    exit 1
fi

# Check if requirements are installed
echo "Checking and installing requirements..."
pip3 install -r requirements.txt

# Run the Flask server
echo
echo "Starting Flask server..."
echo
python3 app.py

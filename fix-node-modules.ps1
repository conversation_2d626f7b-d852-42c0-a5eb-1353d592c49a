Write-Host "========== Fixing Node Modules ==========" -ForegroundColor Cyan

# Check if printer.js exists in @babel/generator
$printerPath = "node_modules\@babel\generator\lib\printer.js"
if (-not (Test-Path $printerPath)) {
    Write-Host "Creating missing printer.js file..." -ForegroundColor Yellow
    
    # Create the directory if it doesn't exist
    $printerDir = "node_modules\@babel\generator\lib"
    if (-not (Test-Path $printerDir)) {
        New-Item -ItemType Directory -Path $printerDir -Force | Out-Null
    }
    
    # Create a basic printer.js file
    $printerContent = @"
"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

class Printer {
  constructor() {
    this.print = () => {};
    this.printList = () => {};
    this.printBlock = () => {};
    this.generateComment = () => {};
    this.printAssertions = () => {};
    this.printString = () => {};
    this.printNewline = () => {};
  }
}

var _default = Printer;
exports.default = _default;
"@
    
    Set-Content -Path $printerPath -Value $printerContent
    Write-Host "Created printer.js file" -ForegroundColor Green
}

# Fix permissions
Write-Host "Setting read permissions for node_modules..." -ForegroundColor Yellow
Get-ChildItem -Path "node_modules" -Recurse -Force | ForEach-Object {
    try {
        $acl = Get-Acl $_.FullName
        $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("Everyone", "ReadAndExecute", "Allow")
        $acl.SetAccessRule($accessRule)
        Set-Acl $_.FullName $acl
    }
    catch {
        Write-Host "Could not set permissions for $($_.FullName): $_" -ForegroundColor Red
    }
}

Write-Host "========== Node Modules Fixed ==========" -ForegroundColor Cyan

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSettings } from '../contexts/SettingsContext';

/**
 * DeviceItem Component
 * 
 * Displays a single device in the device list with name, status, and selection state
 */
const DeviceItem = ({ 
  device, 
  selected, 
  onSelect 
}) => {
  const { darkModeEnabled } = useSettings();

  // Determine the status icon and color based on device properties
  const getStatusIcon = () => {
    // Check if device is connected (from Bluetooth context)
    if (device.isConnected) {
      return {
        name: 'bluetooth',
        color: '#4CAF50' // Green for connected
      };
    }

    // Check if it's an Arduino or EEG device (prioritized)
    if (device.isArduino || device.isEEG) {
      return {
        name: 'hardware-chip',
        color: '#9C27B0' // Purple for EEG/Arduino devices
      };
    }

    // Check RSSI strength for general availability
    if (device.rssi) {
      if (device.rssi > -60) {
        return {
          name: 'radio',
          color: '#4CAF50' // Strong signal - Green
        };
      } else if (device.rssi > -80) {
        return {
          name: 'radio-outline',
          color: '#FFC107' // Medium signal - Amber
        };
      } else {
        return {
          name: 'radio-outline',
          color: '#FF5722' // Weak signal - Red
        };
      }
    }

    // Default for available devices
    return {
      name: 'radio-outline',
      color: '#2196F3' // Blue for available
    };
  };

  const statusIcon = getStatusIcon();

  // Get status text based on device properties
  const getStatusText = () => {
    if (device.isConnected) {
      return 'Connected';
    }

    if (device.isArduino) {
      return 'Arduino EEG Device';
    }

    if (device.isEEG) {
      return 'EEG Device';
    }

    // Show signal strength based on RSSI
    if (device.rssi) {
      if (device.rssi > -60) {
        return 'Strong Signal';
      } else if (device.rssi > -80) {
        return 'Medium Signal';
      } else {
        return 'Weak Signal';
      }
    }

    return 'Available';
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: darkModeEnabled 
            ? selected ? 'rgba(156, 39, 176, 0.3)' : 'rgba(30, 30, 30, 0.7)'
            : selected ? 'rgba(156, 39, 176, 0.1)' : 'rgba(255, 255, 255, 0.7)',
          borderColor: selected 
            ? '#9C27B0' // Purple when selected
            : darkModeEnabled ? 'rgba(100, 100, 100, 0.5)' : 'rgba(200, 200, 200, 0.8)',
        }
      ]}
      onPress={() => onSelect(device.id)}
      activeOpacity={0.7}
    >
      {/* Device Icon */}
      <View style={[
        styles.iconContainer,
        {
          backgroundColor: darkModeEnabled ? '#333333' : '#f0f0f0'
        }
      ]}>
        <Ionicons
          name={device.isArduino || device.isEEG ? "hardware-chip" : "bluetooth-outline"}
          size={24}
          color={device.isArduino || device.isEEG ? '#9C27B0' : (darkModeEnabled ? '#9C27B0' : '#9C27B0')}
        />
      </View>

      {/* Device Info */}
      <View style={styles.infoContainer}>
        <Text style={[
          styles.deviceName,
          { color: darkModeEnabled ? '#ffffff' : '#333333' }
        ]}>
          {device.name || device.localName || 'Unknown Device'}
        </Text>
        <View style={styles.statusContainer}>
          <Ionicons
            name={statusIcon.name}
            size={14}
            color={statusIcon.color}
            style={styles.statusIcon}
          />
          <Text style={[
            styles.statusText,
            { color: darkModeEnabled ? '#e0e0e0' : '#666666' }
          ]}>
            {getStatusText()}
          </Text>
        </View>
      </View>

      {/* Selection Indicator */}
      <View style={styles.selectionContainer}>
        {selected ? (
          <Ionicons 
            name="checkmark-circle" 
            size={24} 
            color="#9C27B0" 
          />
        ) : (
          <Ionicons 
            name="ellipse-outline" 
            size={24} 
            color={darkModeEnabled ? '#666666' : '#999999'} 
          />
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    marginVertical: 6,
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: '#000000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 2,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  infoContainer: {
    flex: 1,
  },
  deviceName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIcon: {
    marginRight: 4,
  },
  statusText: {
    fontSize: 14,
  },
  selectionContainer: {
    marginLeft: 10,
  },
});

export default DeviceItem;

# Troubleshooting Guide for Synaptix Authentication

## Common Issues and Solutions

### Error 500 from Development Server

If you encounter a "response code error: 500" from the development server, try these solutions:

1. **Restart the Development Server**

   ```
   # Stop the current server (Ctrl+C)
   # Clear Metro bundler cache
   npx expo start --clear
   ```

2. **Check for Port Conflicts**
   If another process is using port 8081, allow Expo to use an alternative port when prompted.

3. **Update Dependencies**

   ```
   npm install
   ```

4. **Clear Node Modules and Reinstall**
   ```
   rm -rf node_modules
   npm install
   ```

### Authentication Issues

1. **Account Switching Not Working**

   - Make sure you've saved your account information when prompted
   - Check that you're using the latest version of the code

2. **Login Fails After Account Selection**
   - This might happen if the password wasn't saved
   - Try logging in manually with your credentials

## Debugging Tips

1. **Enable Developer Tools**

   - Shake your device or press `m` in the terminal to open the developer menu
   - Select "Debug JS Remotely" to see console logs in your browser

2. **Check Console Logs**

   - Look for error messages in the terminal where you're running the development server
   - Check browser console logs if using web or remote debugging

3. **Verify Storage**
   - You can check if accounts are being saved by adding this code temporarily to a screen:
   ```javascript
   useEffect(() => {
     const checkStorage = async () => {
       const accounts = await AsyncStorage.getItem("savedAccounts");
       console.log("Saved accounts:", accounts);
     };
     checkStorage();
   }, []);
   ```

## Reset Everything

If all else fails, you can reset the app's storage:

```javascript
// Add this function to a screen temporarily
const resetStorage = async () => {
  await AsyncStorage.clear();
  await SecureStore.deleteAllKeysAsync(); // Requires expo-secure-store
  Alert.alert("Storage Reset", "All stored data has been cleared.");
};

// Add a button to call this function
<TouchableOpacity onPress={resetStorage}>
  <Text>Reset All Storage</Text>
</TouchableOpacity>;
```

## Contact Support

If you continue to experience issues, please provide:

1. The exact error message
2. Steps to reproduce the issue
3. Device/emulator information
4. Screenshots if applicable

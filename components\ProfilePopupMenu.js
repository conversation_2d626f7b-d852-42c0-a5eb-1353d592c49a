import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  SafeAreaView,
  Platform,
  Modal,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import { useSettings } from '../contexts/SettingsContext';

const ProfilePopupMenu = ({ visible, onClose, onProfilePress, onSettingsPress }) => {
  const { currentUser, logout } = useAuth();
  const { width } = Dimensions.get('window');
  const { darkModeEnabled } = useSettings();

  // Animation value for sliding from right
  const slideAnim = useRef(new Animated.Value(width)).current;

  // Run animation when visibility changes
  useEffect(() => {
    if (visible) {
      // Reset position when modal opens
      slideAnim.setValue(width);

      // Animate sliding in from right - using spring for smoother motion
      Animated.spring(slideAnim, {
        toValue: 0,
        tension: 65,
        friction: 11,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, width]);

  // Handle closing with animation
  const handleClose = () => {
    // Animate sliding out to the right - using timing for predictable exit
    Animated.timing(slideAnim, {
      toValue: width,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      // Call the parent's onClose after animation completes
      onClose();
    });
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={handleClose}
    >
      <View style={styles.modalContainer}>
        {/* Left side backdrop (touchable to close) */}
        <TouchableOpacity
          style={styles.backdropTouchable}
          activeOpacity={1}
          onPress={handleClose}
        />

        {/* Right side menu panel with slide animation */}
        <Animated.View
          style={[
            styles.menuContainerWrapper,
            { transform: [{ translateX: slideAnim }] }
          ]}>
          <SafeAreaView style={[
            styles.menuContainer,
            {
              backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff',
              borderLeftColor: darkModeEnabled ? '#333' : '#e0e0e0'
            }
          ]}>
        {/* Close button */}
        <TouchableOpacity
          style={styles.closeButton}
          onPress={handleClose}
        >
          <Ionicons name="close" size={24} color={darkModeEnabled ? "#cccccc" : "#121212"} />
        </TouchableOpacity>

        {/* Profile section - centered */}
        <View style={styles.profileSection}>
          <TouchableOpacity
            style={styles.profileImageContainer}
            onPress={() => {
              handleClose();
              setTimeout(() => onProfilePress(), 220);
            }}
          >
            {currentUser?.profilePicture ? (
              <Image
                source={{ uri: currentUser.profilePicture }}
                style={styles.profileImage}
              />
            ) : (
              <View style={[
                styles.profilePlaceholder,
                { backgroundColor: darkModeEnabled ? '#cccccc' : '#666666' } // Light white in dark theme, dark grey in light theme
              ]}>
                <Text style={[
                  styles.profilePlaceholderText,
                  { color: darkModeEnabled ? '#333333' : '#ffffff' } // Dark text on light background, white text on dark background
                ]}>
                  {currentUser?.name ? currentUser.name.charAt(0).toUpperCase() : '?'}
                </Text>
              </View>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              handleClose();
              setTimeout(() => onProfilePress(), 220);
            }}
          >
            <Text style={[
            styles.profileName,
            { color: darkModeEnabled ? '#cccccc' : '#121212' }
          ]}>
              {currentUser?.name || 'User'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Divider */}
        <View style={[
          styles.divider,
          { backgroundColor: darkModeEnabled ? '#333' : '#e0e0e0' }
        ]} />

        {/* Menu options */}
        <View style={styles.menuOptions}>
          {/* Profile option */}
          <TouchableOpacity
            style={[
              styles.menuItem,
              darkModeEnabled ? null : { backgroundColor: 'rgba(0,0,0,0.05)' }
            ]}
            onPress={() => {
              handleClose();
              setTimeout(() => onProfilePress(), 220);
            }}
          >
            <Ionicons
              name="person-outline"
              size={24}
              color={darkModeEnabled ? "#cccccc" : "#666666"}
              style={styles.menuIcon}
            />
            <Text style={[
              styles.menuText,
              { color: darkModeEnabled ? '#cccccc' : '#121212' }
            ]}>View Profile</Text>
          </TouchableOpacity>

          {/* Settings option */}
          <TouchableOpacity
            style={[
              styles.menuItem,
              darkModeEnabled ? null : { backgroundColor: 'rgba(0,0,0,0.05)' }
            ]}
            onPress={() => {
              handleClose();
              setTimeout(() => onSettingsPress(), 220);
            }}
          >
            <Ionicons
              name="help-circle-outline"
              size={24}
              color={darkModeEnabled ? "#cccccc" : "#666666"}
              style={styles.menuIcon}
            />
            <Text style={[
              styles.menuText,
              { color: darkModeEnabled ? '#cccccc' : '#121212' }
            ]}>Help Center</Text>
          </TouchableOpacity>
        </View>

        {/* Spacer to push logout to bottom */}
        <View style={styles.spacer} />

        {/* Logout button at bottom */}
        <View style={[
          styles.logoutContainer,
          {
            borderTopColor: darkModeEnabled ? '#333' : '#e0e0e0'
          }
        ]}>
          <TouchableOpacity
            style={[
              styles.logoutButton,
              darkModeEnabled ? null : { backgroundColor: 'rgba(255,82,82,0.1)' }
            ]}
            onPress={() => {
              handleClose();
              setTimeout(() => {
                logout();
              }, 220);
            }}
          >
            <Ionicons name="log-out-outline" size={24} color="#ff5252" style={styles.logoutIcon} />
            <Text style={styles.logoutText}>Logout</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    flexDirection: 'row',
  },
  backdropTouchable: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  menuContainerWrapper: {
    position: 'absolute',
    right: 0,
    top: 0,
    width: '48%',
    height: '100%',
    shadowColor: '#000',
    shadowOffset: { width: -2, height: 0 },
    shadowOpacity: 0.25,
    shadowRadius: 5,
    elevation: 10,
    overflow: 'hidden', // Improve rendering performance
  },
  menuContainer: {
    flex: 1,
    borderLeftWidth: 1,
    paddingTop: 20,
    paddingHorizontal: 15,
    backfaceVisibility: 'hidden', // Improve rendering performance
  },
  closeButton: {
    alignSelf: 'flex-end',
    padding: 10,
  },
  profileSection: {
    alignItems: 'center',
    marginTop: 30,
    marginBottom: 30,
  },
  profileImageContainer: {
    marginBottom: 15,
  },
  profileImage: {
    width: 90,
    height: 90,
    borderRadius: 45,
  },
  profilePlaceholder: {
    width: 90,
    height: 90,
    borderRadius: 45,
    backgroundColor: '#4fc3f7', // This will be overridden by inline style
    justifyContent: 'center',
    alignItems: 'center',
  },
  profilePlaceholderText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  divider: {
    height: 1,
    marginVertical: 20,
  },
  menuOptions: {
    marginTop: 10,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    marginBottom: 8,
  },
  menuIcon: {
    marginRight: 12,
    width: 22,
  },
  menuText: {
    fontSize: 16,
  },
  spacer: {
    flex: 1,
    minHeight: 20,
  },
  logoutContainer: {
    paddingBottom: 30,
    paddingHorizontal: 10,
    borderTopWidth: 1,
    paddingTop: 20,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
  },
  logoutIcon: {
    marginRight: 12,
    width: 22,
  },
  logoutText: {
    fontSize: 16,
    color: '#ff5252',
    fontWeight: 'bold',
  },
});

export default ProfilePopupMenu;

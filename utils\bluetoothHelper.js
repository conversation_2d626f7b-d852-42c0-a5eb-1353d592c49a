import { Platform, PermissionsAndroid } from 'react-native';
import { <PERSON><PERSON><PERSON> } from 'buffer';

// Check if we're running in an environment that supports native modules
let BleManager;
let isBleAvailable = false;

try {
  // Try to import the BLE library
  const bleLibrary = require('react-native-ble-plx');
  BleManager = bleLibrary.BleManager;

  // Just check if the class exists, don't create a test instance
  // Creating and destroying instances can cause issues
  if (BleManager && typeof BleManager === 'function') {
    isBleAvailable = true;
    console.log('✅ BLE library is available');
  } else {
    console.warn('⚠️ BLE library imported but BleManager class not available');
    isBleAvailable = false;
  }
} catch (importError) {
  console.warn('⚠️ react-native-ble-plx not available:', importError.message);
  isBleAvailable = false;
}

// Create mock BLE manager for environments without native support
if (!isBleAvailable) {
  console.log('🔄 Using mock BLE implementation for development');
  BleManager = class MockBleManager {
    constructor() {
      console.log('📱 Mock BLE Manager initialized');
    }

    state() {
      return Promise.resolve('PoweredOn');
    }

    startDeviceScan(serviceUUIDs, options, listener) {
      console.log('🔍 Mock: Starting device scan');
      // Simulate finding some mock devices after a delay
      setTimeout(() => {
        if (listener) {
          // Create mock EEG device
          const mockDevice = {
            id: 'mock-eeg-device-001',
            name: 'Mock EEG Device',
            localName: 'Arduino Nano 33 BLE',
            rssi: -45,
            isConnectable: true,
            serviceUUIDs: ['12345678-1234-1234-1234-123456789abc'],
            connect: () => Promise.resolve({
              id: 'mock-eeg-device-001',
              name: 'Mock EEG Device',
              isConnected: true,
              services: () => Promise.resolve([]),
              discoverAllServicesAndCharacteristics: () => Promise.resolve()
            })
          };
          listener(null, mockDevice);
        }
      }, 2000);
    }

    stopDeviceScan() {
      console.log('⏹️ Mock: Stopping device scan');
    }

    connectToDevice() {
      return Promise.reject(new Error('Mock BLE: Connect to a real device for actual functionality'));
    }

    onDeviceDisconnected() {
      console.log('📡 Mock: Setting up disconnect listener');
      return { remove: () => console.log('Mock: Disconnect listener removed') };
    }

    destroy() {
      console.log('🗑️ Mock: BLE Manager destroyed');
    }
  };
}

// Create a singleton BLE manager instance
let bleManagerInstance = null;

/**
 * Check if BLE is available in the current environment
 * @returns {boolean} Whether BLE is available
 */
export const isBleSupported = () => {
  return isBleAvailable;
};

/**
 * Get a user-friendly status message about BLE availability
 * @returns {string} Status message
 */
export const getBleStatus = () => {
  if (isBleAvailable) {
    return 'Bluetooth Low Energy is available and ready';
  } else {
    return 'Running in development mode with mock Bluetooth functionality';
  }
};

/**
 * Check if the BLE manager is currently available and not destroyed
 * @returns {boolean} Whether the BLE manager is available
 */
export const isBleManagerAvailable = () => {
  try {
    return bleManagerInstance && typeof bleManagerInstance.state === 'function';
  } catch (error) {
    return false;
  }
};

// EEG Device Service and Characteristic UUIDs
// Standard UUIDs that might be used by Arduino Nano 33 BLE
const EEG_SERVICE_UUID = '0000180d-0000-1000-8000-00805f9b34fb'; // Heart Rate service (common example)
const EEG_DATA_CHARACTERISTIC_UUID = '00002a37-0000-1000-8000-00805f9b34fb'; // Heart Rate Measurement characteristic
const EEG_CONTROL_CHARACTERISTIC_UUID = '00002a39-0000-1000-8000-00805f9b34fb'; // Heart Rate Control Point

// Arduino Nano 33 BLE specific UUIDs
// These are common UUIDs used in Arduino examples, but your specific device might use different ones
const ARDUINO_SERVICE_UUID = '19b10000-e8f2-537e-4f6c-d104768a1214'; // Common Arduino example service UUID
const ARDUINO_CHARACTERISTIC_UUID = '19b10001-e8f2-537e-4f6c-d104768a1214'; // Common Arduino example characteristic UUID

// Generic Attribute Service (GATT) UUIDs that might be useful for discovery
const DEVICE_INFORMATION_SERVICE_UUID = '0000180a-0000-1000-8000-00805f9b34fb';
const MANUFACTURER_NAME_CHAR_UUID = '00002a29-0000-1000-8000-00805f9b34fb';

// Active subscriptions
const activeSubscriptions = new Map();

/**
 * Get the BLE manager instance (singleton pattern)
 * @returns {BleManager} The BLE manager instance
 */
export const getBleManager = () => {
  try {
    // Always return the same instance if it exists and hasn't been destroyed
    if (bleManagerInstance) {
      // Check if the instance is still valid
      try {
        // Try to access a property to see if it's destroyed
        if (bleManagerInstance.state) {
          return bleManagerInstance;
        }
      } catch (checkError) {
        // Instance is destroyed, need to create a new one
        console.log('🔄 Previous BLE manager instance was destroyed, creating new one');
        bleManagerInstance = null;
      }
    }

    // Create new instance based on availability
    if (isBleAvailable) {
      console.log('✅ Creating real BLE manager instance');
      try {
        bleManagerInstance = new BleManager();
        return bleManagerInstance;
      } catch (realBleError) {
        console.error('❌ Failed to create real BLE manager:', realBleError);
        // Fall through to mock creation
      }
    }

    // Create mock manager (either because BLE is not available or real BLE failed)
    console.log('📱 Creating mock BLE manager instance');
    if (!isBleAvailable) {
      // Use the mock class if BLE is not available
      bleManagerInstance = new MockBleManager();
    } else {
      // If BLE is available but failed, try one more time with error handling
      try {
        bleManagerInstance = new BleManager();
      } catch (finalError) {
        console.error('❌ Final attempt to create BLE manager failed, using mock:', finalError);
        bleManagerInstance = new MockBleManager();
      }
    }

    return bleManagerInstance;
  } catch (error) {
    console.error('❌ Error creating BLE manager instance:', error);

    // Last resort: create a simple fallback mock
    try {
      console.log('🔄 Creating fallback mock BLE manager');
      const FallbackMockBleManager = class {
        constructor() {
          console.log('📱 Fallback Mock BLE Manager initialized');
        }
        state() {
          return Promise.resolve('PoweredOn');
        }
        startDeviceScan(serviceUUIDs, options, listener) {
          console.log('🔍 Fallback Mock: Starting device scan');
          // Simulate finding a mock device after delay
          setTimeout(() => {
            if (listener) {
              const mockDevice = {
                id: 'mock-device-001',
                name: 'Mock EEG Device',
                rssi: -45,
                isConnectable: true
              };
              listener(null, mockDevice);
            }
          }, 2000);
        }
        stopDeviceScan() {
          console.log('⏹️ Fallback Mock: Stopping device scan');
        }
        destroy() {
          console.log('🗑️ Fallback Mock: BLE Manager destroyed');
        }
      };

      bleManagerInstance = new FallbackMockBleManager();
      return bleManagerInstance;
    } catch (mockError) {
      console.error('❌ Even fallback mock BLE manager failed:', mockError);
      throw new Error('Failed to initialize any Bluetooth manager');
    }
  }
};

/**
 * Check if Bluetooth permissions are granted
 * @returns {Promise<boolean>} Whether permissions are granted
 */
export const checkBluetoothPermissions = async () => {
  if (Platform.OS !== 'android') {
    return true; // iOS handles permissions differently
  }

  try {
    if (Platform.Version >= 31) { // Android 12+
      const bluetoothScan = await PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN
      );

      const bluetoothConnect = await PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT
      );

      const fineLocation = await PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
      );

      return bluetoothScan && bluetoothConnect && fineLocation;
    } else { // Android 11 and below
      const fineLocation = await PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
      );

      return fineLocation;
    }
  } catch (error) {
    console.error('Error checking Bluetooth permissions:', error);
    return false;
  }
};

/**
 * Request Bluetooth permissions on Android
 * @returns {Promise<boolean>} Whether permissions were granted
 */
export const requestBluetoothPermissions = async () => {
  if (Platform.OS !== 'android') {
    return true;
  }

  try {
    // First check if permissions are already granted
    const permissionsGranted = await checkBluetoothPermissions();
    if (permissionsGranted) {
      return true;
    }

    if (Platform.Version >= 31) { // Android 12+
      // Request all permissions at once with a more detailed explanation
      const permissions = [
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
      ];

      const granted = await PermissionsAndroid.requestMultiple(permissions);

      // Check if all permissions were granted
      const allGranted = Object.values(granted).every(
        status => status === PermissionsAndroid.RESULTS.GRANTED
      );

      if (!allGranted) {
        console.log('Some Bluetooth permissions were denied:', granted);
      }

      return allGranted;
    } else { // Android 11 and below
      // For older Android versions, we only need location permission
      const fineLocationPermission = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: 'Location Permission Required',
          message: 'Synaptix needs access to your location to scan for and connect to Bluetooth devices. This is required by Android for Bluetooth functionality.',
          buttonPositive: 'Allow',
          buttonNegative: 'Deny',
        }
      );

      return fineLocationPermission === PermissionsAndroid.RESULTS.GRANTED;
    }
  } catch (error) {
    console.error('Error requesting Bluetooth permissions:', error);
    return false;
  }
};

/**
 * Start scanning for Bluetooth devices
 * @param {Function} onDeviceFound Callback when a device is found
 * @param {Function} onError Callback when an error occurs
 * @returns {Function} Function to stop scanning
 */
export const startScan = (onDeviceFound, onError) => {
  try {
    const manager = getBleManager();

    // Check if Bluetooth is enabled before scanning
    manager.state().then(state => {
      if (state !== 'PoweredOn') {
        const error = new Error('Bluetooth is not powered on');
        console.error('Bluetooth scan error:', error);
        if (onError) onError(error);
        return;
      }

      // Start scanning with options
      manager.startDeviceScan(
        null, // null means scan for all services
        {
          allowDuplicates: false,
          scanMode: Platform.OS === 'android' ? 1 : undefined, // 1 = SCAN_MODE_LOW_LATENCY on Android
          numberOfMatches: Platform.OS === 'android' ? 3 : undefined, // Find more devices
        },
        (error, device) => {
          if (error) {
            console.error('Bluetooth scan error:', error);
            if (onError) onError(error);
            return;
          }

          // Process the device if it exists
          if (device) {
            // Check if it's an Arduino Nano 33 BLE or other EEG device
            const isArduino = isArduinoNano33BLE(device);
            const isEEG = isEEGDevice(device);

            // Log device information for debugging
            console.log(`Found device: ${device.name || 'Unnamed'} (${device.id}), RSSI: ${device.rssi}`);

            // Add additional properties to the device
            const enhancedDevice = {
              ...device,
              isArduino,
              isEEG
            };

            // Return the device to the caller
            if (onDeviceFound) {
              // Return all devices with a name, or Arduino/EEG devices even without a name
              if (device.name || isArduino || isEEG) {
                onDeviceFound(enhancedDevice);
              }
            }
          }
        }
      );
    }).catch(error => {
      console.error('Error checking Bluetooth state:', error);
      if (onError) onError(error);
    });

    // Return a function to stop scanning
    return () => {
      try {
        manager.stopDeviceScan();
        console.log('Bluetooth scan stopped');
      } catch (error) {
        console.error('Error stopping Bluetooth scan:', error);
      }
    };
  } catch (error) {
    console.error('Error starting Bluetooth scan:', error);
    if (onError) onError(error);

    // Return a no-op function if we couldn't start scanning
    return () => {};
  }
};

/**
 * Connect to a Bluetooth device
 * @param {Object} device The device to connect to
 * @param {Function} onConnected Callback when connected
 * @param {Function} onDisconnected Callback when disconnected
 * @param {Function} onError Callback when an error occurs
 * @returns {Promise<Object>} The connected device
 */
export const connectToDevice = async (device, onConnected, onDisconnected, onError) => {
  const manager = getBleManager();

  try {
    // Connect to the device
    const connectedDevice = await device.connect();

    // Discover all services and characteristics
    const discoveredDevice = await connectedDevice.discoverAllServicesAndCharacteristics();

    // Set up disconnection listener
    manager.onDeviceDisconnected(device.id, (error, disconnectedDevice) => {
      if (error) {
        console.error('Disconnection error:', error);
        if (onError) onError(error);
      }

      if (onDisconnected) onDisconnected(disconnectedDevice);
    });

    if (onConnected) onConnected(discoveredDevice);

    return discoveredDevice;
  } catch (error) {
    console.error('Connection error:', error);
    if (onError) onError(error);
    throw error;
  }
};

/**
 * Disconnect from a device
 * @param {Object} device The device to disconnect from
 */
export const disconnectDevice = async (device) => {
  if (!device) return;

  const manager = getBleManager();

  try {
    await manager.cancelDeviceConnection(device.id);
  } catch (error) {
    console.error('Disconnect error:', error);
  }
};

/**
 * Clean up Bluetooth resources
 * This function is implemented below with subscription cleanup
 */

/**
 * Subscribe to EEG data from a device
 * @param {Object} device The device to subscribe to
 * @param {Function} onDataReceived Callback when data is received
 * @param {Function} onError Callback when an error occurs
 * @returns {Promise<void>}
 */
export const subscribeToEEGData = async (device, onDataReceived, onError) => {
  if (!device) return;

  try {
    // Check if the device has the EEG service
    // Handle both function and array cases for device.services
    let services;
    try {
      if (typeof device.services === 'function') {
        services = await device.services();
      } else if (Array.isArray(device.services)) {
        services = device.services;
      } else {
        // If no services available, try to discover them
        console.log('No services found, attempting to discover...');
        const discoveredDevice = await device.discoverAllServicesAndCharacteristics();
        if (typeof discoveredDevice.services === 'function') {
          services = await discoveredDevice.services();
        } else {
          services = [];
        }
      }
    } catch (servicesError) {
      console.error('Error getting device services:', servicesError);
      services = [];
    }

    let eegServiceUUID = EEG_SERVICE_UUID;

    // If the device doesn't have the expected service UUID, try to find a suitable service
    // This is useful for testing with different devices
    if (!services.some(service => service.uuid === EEG_SERVICE_UUID)) {
      console.log('EEG service not found, searching for alternative services...');

      // Check if it's an Arduino Nano 33 BLE by looking for the Arduino service UUID
      const arduinoService = services.find(service =>
        service.uuid.toLowerCase() === ARDUINO_SERVICE_UUID.toLowerCase() ||
        service.uuid.toLowerCase().includes('19b10000')  // Common Arduino service prefix
      );

      if (arduinoService) {
        console.log('Arduino Nano 33 BLE service found:', arduinoService.uuid);
        eegServiceUUID = arduinoService.uuid;
      } else {
        // Look for any service that might be related to data streaming
        const potentialService = services.find(service =>
          service.uuid.toLowerCase().includes('180d') || // Heart rate service
          service.uuid.toLowerCase().includes('180f') || // Battery service
          service.uuid.toLowerCase().includes('1800') || // Generic access
          service.uuid.toLowerCase().includes('1801') || // Generic attribute
          service.uuid.toLowerCase().includes('180a')    // Device information service
        );

        if (potentialService) {
          eegServiceUUID = potentialService.uuid;
          console.log('Using alternative service:', eegServiceUUID);
        } else {
          // If no specific service is found, just use the first available service
          if (services.length > 0) {
            eegServiceUUID = services[0].uuid;
            console.log('Using first available service:', eegServiceUUID);
          } else {
            console.error('No suitable service found on device');
            if (onError) onError(new Error('No suitable service found on device'));
            return;
          }
        }
      }
    }

    // Get characteristics for the service
    const characteristics = await device.characteristicsForService(eegServiceUUID);
    let eegDataCharUUID = EEG_DATA_CHARACTERISTIC_UUID;

    // If the device doesn't have the expected characteristic UUID, try to find a suitable characteristic
    if (!characteristics.some(char => char.uuid === EEG_DATA_CHARACTERISTIC_UUID)) {
      console.log('EEG data characteristic not found, searching for alternatives...');

      // Check for Arduino Nano 33 BLE characteristic
      const arduinoChar = characteristics.find(char =>
        char.uuid.toLowerCase() === ARDUINO_CHARACTERISTIC_UUID.toLowerCase() ||
        char.uuid.toLowerCase().includes('19b10001')  // Common Arduino characteristic prefix
      );

      if (arduinoChar) {
        console.log('Arduino Nano 33 BLE characteristic found:', arduinoChar.uuid);
        eegDataCharUUID = arduinoChar.uuid;
      } else {
        // Look for any characteristic that supports notifications
        const notifiableChar = characteristics.find(char =>
          char.isNotifiable || char.isIndicatable
        );

        if (notifiableChar) {
          eegDataCharUUID = notifiableChar.uuid;
          console.log('Using notifiable characteristic:', eegDataCharUUID);
        } else {
          // If no notifiable characteristic is found, just use the first available characteristic
          if (characteristics.length > 0) {
            eegDataCharUUID = characteristics[0].uuid;
            console.log('Using first available characteristic:', eegDataCharUUID);
          } else {
            console.error('No suitable characteristic found on device');
            if (onError) onError(new Error('No suitable characteristic found on device'));
            return;
          }
        }
      }
    }

    // Subscribe to the characteristic
    const subscription = device.monitorCharacteristicForService(
      eegServiceUUID,
      eegDataCharUUID,
      (error, characteristic) => {
        if (error) {
          console.error('Characteristic monitoring error:', error);
          if (onError) onError(error);
          return;
        }

        if (characteristic?.value) {
          try {
            // Parse the data
            const parsedData = parseEEGData(characteristic.value);
            if (onDataReceived) onDataReceived(parsedData);
          } catch (parseError) {
            console.error('Data parsing error:', parseError);
            if (onError) onError(parseError);
          }
        }
      }
    );

    // Store the subscription for later cleanup
    activeSubscriptions.set(device.id, subscription);

  } catch (error) {
    console.error('EEG data subscription error:', error);
    if (onError) onError(error);
  }
};

/**
 * Parse EEG data from a Base64 encoded string
 * @param {string} base64Data Base64 encoded data
 * @returns {Object} Parsed EEG data
 */
const parseEEGData = (base64Data) => {
  // Decode the Base64 data
  const buffer = Buffer.from(base64Data, 'base64');

  // Try to detect if this is Arduino Nano 33 BLE data format
  // Arduino often sends data in specific formats like CSV strings or binary formats

  // First, try to interpret as a string (common Arduino format)
  let channels = [];
  let isArduinoFormat = false;

  try {
    // Convert buffer to string and check if it's a CSV format
    const dataString = buffer.toString('utf8');

    // Check if it's a comma-separated or space-separated string
    if (dataString.includes(',') || dataString.includes(' ')) {
      // Split by comma or space
      const separator = dataString.includes(',') ? ',' : ' ';
      const values = dataString.split(separator).map(val => parseFloat(val.trim()));

      // Filter out NaN values
      channels = values.filter(val => !isNaN(val));
      isArduinoFormat = true;
      console.log('Detected Arduino CSV format:', channels);
    }
  } catch (e) {
    console.log('Not a string format, trying binary format');
  }

  // If not a string format, try binary format (common for Arduino Nano 33 BLE)
  if (!isArduinoFormat) {
    // Arduino often sends int16 values (2 bytes per value)
    // Try to interpret the buffer as int16 values
    if (buffer.length >= 2 && buffer.length % 2 === 0) {
      channels = [];
      for (let i = 0; i < buffer.length; i += 2) {
        // Combine two bytes into a 16-bit integer (little-endian)
        const value = buffer[i] | (buffer[i + 1] << 8);
        channels.push(value);
      }
      console.log('Detected Arduino binary format (int16):', channels);
    } else {
      // Fallback to byte array if not divisible by 2
      channels = Array.from(buffer);
      console.log('Using raw byte array:', channels);
    }
  }

  // Ensure we have at least 8 channels for EEG data
  // If we have fewer, pad with zeros
  while (channels.length < 8) {
    channels.push(0);
  }

  // If we have more than 8 channels, take the first 8
  if (channels.length > 8) {
    channels = channels.slice(0, 8);
  }

  // Calculate a simple "brain score" based on the channel values
  // This is just for demonstration - real EEG analysis would be much more complex
  // Normalize values to 0-255 range first
  const normalizedChannels = channels.map(val => {
    // Assuming Arduino values might be in different ranges
    // For analog readings (0-1023), for accelerometer (-32768 to 32767), etc.
    if (val >= -32768 && val <= 32767) {
      // Likely accelerometer or gyroscope data
      return Math.round(((val + 32768) / 65536) * 255);
    } else if (val >= 0 && val <= 1023) {
      // Likely analog reading
      return Math.round((val / 1023) * 255);
    } else {
      // Unknown range, just clamp to 0-255
      return Math.min(255, Math.max(0, val));
    }
  });

  const sum = normalizedChannels.reduce((acc, val) => acc + val, 0);
  const average = sum / normalizedChannels.length;
  const brainScore = Math.min(100, Math.max(0, Math.round((average / 255) * 100)));

  // Create a timestamp
  const timestamp = new Date().getTime();

  // Return the parsed data
  return {
    timestamp,
    channels,
    normalizedChannels,
    brainScore,
    rawData: Array.from(buffer), // Include the raw data for debugging
    isArduinoFormat
  };
};

/**
 * Unsubscribe from EEG data
 * @param {Object} device The device to unsubscribe from
 */
export const unsubscribeFromEEGData = (device) => {
  if (!device) return;

  // Get the subscription for this device
  const subscription = activeSubscriptions.get(device.id);

  if (subscription) {
    // Remove the subscription
    subscription.remove();
    activeSubscriptions.delete(device.id);
  }
};

/**
 * Send a command to the EEG device
 * @param {Object} device The device to send the command to
 * @param {string} command The command to send
 * @returns {Promise<void>}
 */
export const sendCommandToDevice = async (device, command) => {
  if (!device) return;

  try {
    // Convert the command to a Buffer
    const data = Buffer.from(command).toString('base64');

    // Write the data to the control characteristic
    await device.writeCharacteristicWithResponseForService(
      EEG_SERVICE_UUID,
      EEG_CONTROL_CHARACTERISTIC_UUID,
      data
    );

    console.log('Command sent successfully:', command);
  } catch (error) {
    console.error('Error sending command:', error);
    throw error;
  }
};

/**
 * Check if a device is an Arduino Nano 33 BLE device
 * @param {Object} device The device to check
 * @returns {boolean} Whether the device is an Arduino Nano 33 BLE
 */
export const isArduinoNano33BLE = (device) => {
  if (!device) return false;

  // Check device name if available
  if (device.name) {
    const lowerName = device.name.toLowerCase();

    // Check for Arduino Nano 33 BLE specific names
    if (lowerName.includes('arduino') ||
        lowerName.includes('nano') ||
        lowerName.includes('ble') ||
        lowerName.includes('nano33')) {
      return true;
    }
  }

  // Check for Arduino service UUIDs even if the device has no name
  if (device.serviceUUIDs && device.serviceUUIDs.length > 0) {
    for (const uuid of device.serviceUUIDs) {
      const lowerUuid = uuid.toLowerCase();

      // Check for common Arduino service UUIDs
      if (lowerUuid.includes('19b10000') || // Common Arduino service prefix
          lowerUuid === ARDUINO_SERVICE_UUID.toLowerCase()) {
        return true;
      }
    }
  }

  // Check manufacturer data if available (some Arduino devices include manufacturer data)
  if (device.manufacturerData) {
    try {
      // Convert manufacturer data to string and check for Arduino-related keywords
      const manufacturerString = Buffer.from(device.manufacturerData, 'base64').toString();
      if (manufacturerString.toLowerCase().includes('arduino')) {
        return true;
      }
    } catch (error) {
      // Ignore parsing errors
    }
  }

  // Check RSSI as a last resort - Arduino devices typically have strong signal when close
  // This is a very rough heuristic and should be used with caution
  if (device.rssi && device.rssi > -50) {
    // If the device has a very strong signal and no name, it might be an Arduino
    // This is a fallback and not very reliable
    return true;
  }

  return false;
};

/**
 * Check if a device is an EEG device
 * @param {Object} device The device to check
 * @returns {boolean} Whether the device is an EEG device
 */
export const isEEGDevice = (device) => {
  if (!device) return false;

  // First check if it's an Arduino Nano 33 BLE
  // Since we're specifically looking for Arduino Nano 33 BLE for EEG
  if (isArduinoNano33BLE(device)) {
    return true;
  }

  // Check device name if available
  if (device.name) {
    const lowerName = device.name.toLowerCase();

    // Check if the device name contains any of these keywords
    const eegKeywords = [
      'eeg', 'brain', 'neuro', 'mind', 'synaptix', 'neural',
      'muse', 'emotiv', 'openbci', 'neurosity', 'crown', 'headset'
    ];

    if (eegKeywords.some(keyword => lowerName.includes(keyword))) {
      return true;
    }
  }

  // Check for EEG-related service UUIDs
  if (device.serviceUUIDs && device.serviceUUIDs.length > 0) {
    // Common service UUIDs used by EEG devices
    const eegServiceUUIDs = [
      EEG_SERVICE_UUID.toLowerCase(),
      '0000fe80', // Muse headband
      '0000fe8d', // Some Emotiv devices
      '0000180a', // Device Information Service (check if combined with other indicators)
    ];

    for (const uuid of device.serviceUUIDs) {
      const lowerUuid = uuid.toLowerCase();
      if (eegServiceUUIDs.some(eegUuid => lowerUuid.includes(eegUuid))) {
        return true;
      }
    }
  }

  // Check for specific manufacturer IDs
  // Some EEG device manufacturers have specific company IDs
  if (device.manufacturerId) {
    // Example manufacturer IDs (these are examples and may not be accurate)
    const eegManufacturerIds = [
      '0x0171', // Muse by InteraXon
      '0x0483', // Some Emotiv devices
    ];

    if (eegManufacturerIds.includes(device.manufacturerId)) {
      return true;
    }
  }

  // If we have a connected device with services, check for characteristics
  // that might indicate it's an EEG device
  if (device.services) {
    for (const service of device.services) {
      if (service.characteristics) {
        for (const characteristic of service.characteristics) {
          // Check for characteristics that might be used for streaming data
          if (characteristic.isNotifiable || characteristic.isIndicatable) {
            return true;
          }
        }
      }
    }
  }

  // If none of the above checks pass, it's probably not an EEG device
  return false;
};

// Modify the original cleanupBluetooth function to also clean up subscriptions
const originalCleanupBluetooth = () => {
  if (bleManagerInstance) {
    bleManagerInstance.destroy();
    bleManagerInstance = null;
  }
};

// Replace the original cleanupBluetooth function
export const cleanupBluetooth = () => {
  try {
    console.log('🧹 Starting Bluetooth cleanup...');

    // Clean up all subscriptions first
    activeSubscriptions.forEach(subscription => {
      try {
        subscription.remove();
      } catch (subError) {
        console.error('Error removing subscription:', subError);
      }
    });
    activeSubscriptions.clear();

    // Only destroy the manager if it exists and is not already destroyed
    if (bleManagerInstance) {
      try {
        // Check if the manager is still valid before destroying
        if (typeof bleManagerInstance.destroy === 'function') {
          bleManagerInstance.destroy();
          console.log('✅ BLE Manager destroyed successfully');
        }
      } catch (destroyError) {
        console.error('Error destroying BLE manager:', destroyError);
        // Continue anyway to reset the instance
      }

      // Always reset the instance reference
      bleManagerInstance = null;
    }

    console.log('✅ Bluetooth cleanup completed');
  } catch (error) {
    console.error('Error in cleanupBluetooth:', error);
    // Force reset the instance even if cleanup failed
    bleManagerInstance = null;
  }
};

export default {
  getBleManager,
  checkBluetoothPermissions,
  requestBluetoothPermissions,
  startScan,
  connectToDevice,
  disconnectDevice,
  cleanupBluetooth,
  subscribeToEEGData,
  unsubscribeFromEEGData,
  sendCommandToDevice,
  isEEGDevice,
  isArduinoNano33BLE,

  // Export constants for direct access if needed
  ARDUINO_SERVICE_UUID,
  ARDUINO_CHARACTERISTIC_UUID,
  EEG_SERVICE_UUID,
  EEG_DATA_CHARACTERISTIC_UUID,
};

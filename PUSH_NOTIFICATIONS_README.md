# Push Notifications Setup Guide

## Understanding the Issue

Starting with Expo SDK 53, Android push notifications functionality was removed from Expo Go. This means you can no longer test push notifications directly in the Expo Go app. Instead, you need to use a development build of your app.

## Solution: Creating a Development Build

### Prerequisites

1. Make sure you have the following installed:
   - Node.js and npm
   - Android Studio (for Android builds)
   - Xcode (for iOS builds, Mac only)
   - EAS CLI: `npm install -g eas-cli`

### Steps to Create a Development Build

1. **Log in to your Expo account**
   ```bash
   eas login
   ```

   If you don't have an Expo account, create one at https://expo.dev/signup

2. **Configure your project**
   The `eas.json` file has been created for you with the necessary configuration.

3. **Build the development client**
   ```bash
   eas build --profile development --platform android
   ```

   For iOS (Mac only):
   ```bash
   eas build --profile development --platform ios
   ```

4. **Install the development build**
   Once the build is complete, you'll get a URL to download the APK (Android) or a link to TestFlight (iOS).
   Download and install it on your device.

## Testing Push Notifications

1. **Run your app on the development build**
   Your app should now register for push notifications and display the token in the console.

2. **Send a test notification**
   You can use the Expo push notification tool to send a test notification:
   
   ```bash
   npx expo-notifications send --to YOUR_EXPO_PUSH_TOKEN --title "Test Notification" --body "This is a test notification"
   ```

   Replace `YOUR_EXPO_PUSH_TOKEN` with the token printed in your console.

3. **Implement a server-side solution**
   For a production app, you'll need to implement a server-side solution to send push notifications.
   You can use services like:
   - Firebase Cloud Messaging (FCM)
   - Expo's push notification service
   - Your own custom server

## Troubleshooting

1. **Push token not generated**
   - Make sure you're using a development build, not Expo Go
   - Check that you have the necessary permissions
   - Verify that you're testing on a physical device, not an emulator

2. **Notifications not received**
   - Check that your device is connected to the internet
   - Verify that notifications are enabled in your device settings
   - Make sure you're using the correct push token

3. **Build errors**
   - Make sure you have the latest EAS CLI: `npm install -g eas-cli@latest`
   - Check your Expo account has the necessary permissions
   - Verify your app.json configuration is correct

## Additional Resources

- [Expo Push Notifications Documentation](https://docs.expo.dev/push-notifications/overview/)
- [EAS Build Documentation](https://docs.expo.dev/build/introduction/)
- [Expo Development Client](https://docs.expo.dev/develop/development-builds/introduction/)

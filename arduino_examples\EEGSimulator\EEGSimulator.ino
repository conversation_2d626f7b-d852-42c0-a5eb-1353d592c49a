/*
 * EEG Simulator for Arduino Nano 33 BLE
 * 
 * This sketch simulates EEG data and sends it over Bluetooth Low Energy.
 * It's designed to work with the Synaptix app.
 * 
 * Hardware: Arduino Nano 33 BLE
 * 
 * Instructions:
 * 1. Upload this sketch to your Arduino Nano 33 BLE
 * 2. Open the Synaptix app and connect to the device
 * 3. The app will receive simulated EEG data
 */

#include <ArduinoBLE.h>

// BLE Service and Characteristic UUIDs
// These match the UUIDs in the app
#define BLE_UUID_EEG_SERVICE "19b10000-e8f2-537e-4f6c-d104768a1214"
#define BLE_UUID_EEG_CHARACTERISTIC "19b10001-e8f2-537e-4f6c-d104768a1214"

// Create the BLE Service and Characteristic
BLEService eegService(BLE_UUID_EEG_SERVICE);
BLECharacteristic eegCharacteristic(BLE_UUID_EEG_CHARACTERISTIC, 
                                   BLERead | BLENotify, 
                                   20); // 20 bytes buffer size

// Variables for simulating EEG data
unsigned long lastUpdateTime = 0;
const int updateInterval = 100; // Update every 100ms
int channelValues[8]; // 8 EEG channels

void setup() {
  Serial.begin(9600);
  while (!Serial);
  
  Serial.println("EEG Simulator for Arduino Nano 33 BLE");
  
  // Initialize the BLE hardware
  if (!BLE.begin()) {
    Serial.println("Starting BLE failed!");
    while (1);
  }
  
  // Set the local name and advertised service
  BLE.setLocalName("EEG-Nano33");
  BLE.setAdvertisedService(eegService);
  
  // Add the characteristic to the service
  eegService.addCharacteristic(eegCharacteristic);
  
  // Add the service
  BLE.addService(eegService);
  
  // Initialize the EEG data
  for (int i = 0; i < 8; i++) {
    channelValues[i] = random(0, 100);
  }
  
  // Set an initial value for the characteristic
  updateEEGData();
  
  // Start advertising
  BLE.advertise();
  
  Serial.println("Bluetooth device active, waiting for connections...");
}

void loop() {
  // Listen for BLE peripherals to connect
  BLEDevice central = BLE.central();
  
  // If a central is connected to peripheral
  if (central) {
    Serial.print("Connected to central: ");
    Serial.println(central.address());
    
    // While the central is still connected to peripheral
    while (central.connected()) {
      // Update the EEG data at regular intervals
      if (millis() - lastUpdateTime >= updateInterval) {
        updateEEGData();
        lastUpdateTime = millis();
      }
    }
    
    // When the central disconnects
    Serial.print("Disconnected from central: ");
    Serial.println(central.address());
  }
}

// Function to update the simulated EEG data
void updateEEGData() {
  // Create a buffer for the data
  byte dataBuffer[16]; // 8 channels, 2 bytes per channel
  
  // Generate new random values with some continuity from previous values
  for (int i = 0; i < 8; i++) {
    // Add some random change to the previous value
    channelValues[i] += random(-5, 6);
    
    // Keep values in a reasonable range
    if (channelValues[i] < 0) channelValues[i] = 0;
    if (channelValues[i] > 100) channelValues[i] = 100;
    
    // Store the value in the buffer (16-bit, little-endian)
    dataBuffer[i*2] = channelValues[i] & 0xFF;
    dataBuffer[i*2+1] = (channelValues[i] >> 8) & 0xFF;
  }
  
  // Update the characteristic value
  eegCharacteristic.writeValue(dataBuffer, 16);
  
  // Print the values to Serial for debugging
  Serial.print("EEG Data: ");
  for (int i = 0; i < 8; i++) {
    Serial.print(channelValues[i]);
    Serial.print(" ");
  }
  Serial.println();
}

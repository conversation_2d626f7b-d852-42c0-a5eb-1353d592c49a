from flask import Flask, request, jsonify
from flask_cors import CORS
import numpy as np
import random
import time
import json
import os

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Simulated brain wave categories
WAVE_CATEGORIES = [
    'Alpha Waves',
    'Beta Waves',
    'Theta Waves',
    'Delta Waves',
    'Gamma Waves',
    'Cognitive Function',
    'Memory',
    'Attention'
]

# Simulated recommendations based on scores
LOW_SCORE_RECOMMENDATIONS = [
    "Consider exercises to improve your {wave} activity",
    "Regular practice can help enhance your {wave} performance",
    "Try focused meditation to boost your {wave} patterns"
]

MEDIUM_SCORE_RECOMMENDATIONS = [
    "Your {wave} activity is within normal range",
    "Continue with regular mental exercises to maintain {wave} performance",
    "Your {wave} patterns show good potential for improvement"
]

HIGH_SCORE_RECOMMENDATIONS = [
    "Your {wave} activity is excellent",
    "Your {wave} patterns show optimal performance",
    "Continue your current practices to maintain excellent {wave} activity"
]

GENERAL_RECOMMENDATIONS = [
    "Regular mental exercises can help maintain cognitive function",
    "Consider meditation to improve overall brain performance",
    "Ensure adequate sleep for optimal brain health",
    "Stay hydrated for better cognitive performance",
    "Regular physical exercise benefits brain health"
]

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint to verify the API is running"""
    return jsonify({
        'status': 'healthy',
        'timestamp': time.time()
    })

@app.route('/api/predict', methods=['POST'])
def predict():
    """
    Process EEG data and return prediction results
    
    Expected JSON payload:
    {
        "eegData": {
            "channels": [array of channel values],
            "timestamp": timestamp
        }
    }
    
    Returns:
    {
        "overallScore": number,
        "categories": [
            {
                "name": string,
                "score": number,
                "color": string
            }
        ],
        "recommendations": [array of strings]
    }
    """
    try:
        # Get data from request
        data = request.json
        
        # Check if we have the expected data
        if not data or 'eegData' not in data:
            return jsonify({
                'error': 'Invalid request format. Expected eegData object.'
            }), 400
            
        eeg_data = data['eegData']
        
        # If we have real EEG data, use it
        if 'channels' in eeg_data and eeg_data['channels']:
            # Process the EEG data and generate predictions
            result = process_eeg_data(eeg_data['channels'])
            return jsonify(result)
        else:
            # Generate mock data if no real data is provided
            mock_result = generate_mock_results()
            return jsonify(mock_result)
            
    except Exception as e:
        return jsonify({
            'error': str(e)
        }), 500

def process_eeg_data(channel_values):
    """
    Process EEG channel values and return prediction results
    
    Args:
        channel_values: List of EEG channel values
        
    Returns:
        Dictionary with prediction results
    """
    # Normalize channel values to scores between 0-100
    channel_scores = []
    
    # Ensure we have at least 8 channels (pad with random values if needed)
    if len(channel_values) < 8:
        channel_values = channel_values + [random.randint(50, 200) for _ in range(8 - len(channel_values))]
    
    # Use only the first 8 channels if more are provided
    channel_values = channel_values[:8]
    
    # Define colors for visualization
    channel_colors = [
        '#ffffff',  # Alpha - White
        '#7986cb',  # Beta - Blue
        '#4db6ac',  # Theta - Teal
        '#ffb74d',  # Delta - Orange
        '#ff8a65',  # Gamma - Coral
        '#ba68c8',  # Cognitive - Purple
        '#4fc3f7',  # Memory - Light Blue
        '#81c784'   # Attention - Green
    ]
    
    # Normalize values and create category objects
    for i, value in enumerate(channel_values):
        # Normalize to 0-100 range
        normalized_value = min(100, max(0, round((value / 255) * 100)))
        
        channel_scores.append({
            'name': WAVE_CATEGORIES[i % len(WAVE_CATEGORIES)],
            'score': normalized_value,
            'color': channel_colors[i % len(channel_colors)]
        })
    
    # Calculate overall score (weighted average)
    weights = [1.2, 1.5, 0.8, 0.7, 1.0, 1.3, 1.2, 1.4]  # Different weights for different channels
    weighted_sum = sum(score['score'] * weights[i] for i, score in enumerate(channel_scores))
    total_weight = sum(weights[:len(channel_scores)])
    overall_score = min(100, max(0, round(weighted_sum / total_weight)))
    
    # Generate recommendations
    recommendations = generate_recommendations(channel_scores)
    
    return {
        'overallScore': overall_score,
        'categories': channel_scores,
        'recommendations': recommendations
    }

def generate_recommendations(scores):
    """Generate personalized recommendations based on the scores"""
    recommendations = []
    
    # Find the lowest score
    lowest_score = min(scores, key=lambda x: x['score'])
    
    # Find the highest score
    highest_score = max(scores, key=lambda x: x['score'])
    
    # Add recommendation based on lowest score
    if lowest_score['score'] < 70:
        rec = random.choice(LOW_SCORE_RECOMMENDATIONS).format(wave=lowest_score['name'].lower())
        recommendations.append(rec)
    elif lowest_score['score'] < 85:
        rec = random.choice(MEDIUM_SCORE_RECOMMENDATIONS).format(wave=lowest_score['name'].lower())
        recommendations.append(rec)
    
    # Add recommendation based on highest score
    rec = random.choice(HIGH_SCORE_RECOMMENDATIONS).format(wave=highest_score['name'].lower())
    recommendations.append(rec)
    
    # Add 2-3 general recommendations
    general_recs = random.sample(GENERAL_RECOMMENDATIONS, min(3, len(GENERAL_RECOMMENDATIONS)))
    recommendations.extend(general_recs)
    
    return recommendations

def generate_mock_results():
    """Generate mock results for testing"""
    mock_categories = []
    
    for i, category in enumerate(WAVE_CATEGORIES):
        score = random.randint(70, 98)
        mock_categories.append({
            'name': category,
            'score': score,
            'color': ['#ffffff', '#7986cb', '#4db6ac', '#ffb74d', '#ff8a65', '#ba68c8', '#4fc3f7', '#81c784'][i % 8]
        })
    
    # Calculate overall score
    overall_score = round(sum(cat['score'] for cat in mock_categories) / len(mock_categories))
    
    # Generate recommendations
    recommendations = generate_recommendations(mock_categories)
    
    return {
        'overallScore': overall_score,
        'categories': mock_categories,
        'recommendations': recommendations
    }

if __name__ == '__main__':
    # Get port from environment variable or use default
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=True)

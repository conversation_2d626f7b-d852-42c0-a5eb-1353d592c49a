import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Modal,
  Dimensions,
  ActivityIndicator,
  Platform,
  SafeAreaView,
  Alert,
} from 'react-native';
import * as ImageManipulator from 'expo-image-manipulator';
import { Ionicons } from '@expo/vector-icons';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

const SimpleImageEditor = ({ visible, imageUri, onCancel, onDone }) => {
  const [processing, setProcessing] = useState(false);
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
  const [rotation, setRotation] = useState(0);
  const [flipHorizontal, setFlipHorizontal] = useState(false);
  const [flipVertical, setFlipVertical] = useState(false);
  const [editedImageUri, setEditedImageUri] = useState(null);
  const [activeTab, setActiveTab] = useState('crop');
  const [cropAspectRatio, setCropAspectRatio] = useState(null);

  // Load image dimensions when the component mounts or imageUri changes
  useEffect(() => {
    if (imageUri) {
      Image.getSize(imageUri, (width, height) => {
        setImageSize({ width, height });
      });

      // Reset editing state when a new image is loaded
      setRotation(0);
      setFlipHorizontal(false);
      setFlipVertical(false);
      setEditedImageUri(null);
      setActiveTab('crop');
      setCropAspectRatio(null);
    }
  }, [imageUri]);

  // Apply manual crop
  const applyCrop = async () => {
    if (!imageUri) return;

    setProcessing(true);

    try {
      // Build the array of operations to perform
      const operations = [];

      // Apply manual crop
      if (activeTab === 'crop') {
        // Use 80% of the image for manual crop
        const cropWidth = Math.round(imageSize.width * 0.8);
        const cropHeight = Math.round(imageSize.height * 0.8);

        // Center the crop
        const originX = Math.max(0, (imageSize.width - cropWidth) / 2);
        const originY = Math.max(0, (imageSize.height - cropHeight) / 2);

        operations.push({
          crop: {
            originX: Math.round(originX),
            originY: Math.round(originY),
            width: Math.round(cropWidth),
            height: Math.round(cropHeight),
          },
        });
      }

      // Add rotation if needed
      if (rotation !== 0) {
        operations.push({ rotate: rotation });
      }

      // Add flip operations if needed
      if (flipHorizontal) {
        operations.push({ flip: { horizontal: true } });
      }

      if (flipVertical) {
        operations.push({ flip: { vertical: true } });
      }

      // Perform all operations
      const manipResult = await ImageManipulator.manipulateAsync(
        imageUri,
        operations,
        { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG }
      );

      // Save the edited image URI
      setEditedImageUri(manipResult.uri);

      return manipResult.uri;
    } catch (error) {
      console.error('Error applying transformations:', error);
      Alert.alert('Error', 'Failed to process the image. Please try again.');
      return null;
    } finally {
      setProcessing(false);
    }
  };

  // Preview the transformations
  const previewTransformations = async () => {
    const result = await applyCrop();
    if (result) {
      setEditedImageUri(result);
    }
  };

  // Apply transformations and finish editing
  const handleDone = async () => {
    if (!editedImageUri) {
      // If no preview has been generated yet, apply transformations first
      const finalImageUri = await applyCrop();
      if (finalImageUri && onDone) {
        onDone(finalImageUri);
      }
    } else {
      // If preview exists, just use that
      if (onDone) {
        onDone(editedImageUri);
      }
    }
  };

  // Rotate the image left (counter-clockwise)
  const rotateLeft = () => {
    setRotation((prev) => {
      const newRotation = prev - 90;
      return newRotation < 0 ? newRotation + 360 : newRotation;
    });
    if (editedImageUri) setEditedImageUri(null);
  };

  // Rotate the image right (clockwise)
  const rotateRight = () => {
    setRotation((prev) => (prev + 90) % 360);
    if (editedImageUri) setEditedImageUri(null);
  };

  // Flip the image horizontally
  const toggleFlipHorizontal = () => {
    setFlipHorizontal((prev) => !prev);
    if (editedImageUri) setEditedImageUri(null);
  };

  // Flip the image vertically
  const toggleFlipVertical = () => {
    setFlipVertical((prev) => !prev);
    if (editedImageUri) setEditedImageUri(null);
  };

  // Initialize crop with free-form cropping
  const initializeManualCrop = () => {
    // Always use free-form cropping
    setCropAspectRatio(null);
  };

  if (!visible || !imageUri) return null;

  return (
    <Modal
      visible={visible}
      transparent={false}
      animationType="slide"
      onRequestClose={onCancel}
      statusBarTranslucent={true}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.headerButton} onPress={onCancel}>
            <Text style={styles.headerButtonText}>Cancel</Text>
          </TouchableOpacity>

          <Text style={styles.headerTitle}>Edit Photo</Text>

          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleDone}
            disabled={processing}
          >
            <Text style={[styles.headerButtonText, styles.doneButton]}>Done</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.imageContainer}>
          {processing ? (
            <ActivityIndicator size="large" color="#4fc3f7" />
          ) : (
            <>
              <Image
                source={{ uri: editedImageUri || imageUri }}
                style={[
                  styles.image,
                  // Apply transform styles for preview (rotation and flip)
                  {
                    transform: [
                      { rotate: `${rotation}deg` },
                      { scaleX: flipHorizontal ? -1 : 1 },
                      { scaleY: flipVertical ? -1 : 1 }
                    ]
                  }
                ]}
                resizeMode="contain"
              />

              {/* Show manual crop guide */}
              {activeTab === 'crop' && !editedImageUri && (
                <View style={styles.cropOverlay}>
                  <View style={styles.cropGuide}>
                    <View style={styles.cropCorner} />
                    <View style={[styles.cropCorner, styles.topRight]} />
                    <View style={[styles.cropCorner, styles.bottomLeft]} />
                    <View style={[styles.cropCorner, styles.bottomRight]} />

                    {/* Grid lines */}
                    <View style={styles.gridLine} />
                    <View style={[styles.gridLine, styles.horizontalGridLine]} />
                    <View style={[styles.gridLine, styles.verticalGridLine]} />
                  </View>
                </View>
              )}
            </>
          )}
        </View>

        {/* Tabs for different editing modes */}
        <View style={styles.tabsContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'crop' && styles.activeTab]}
            onPress={() => setActiveTab('crop')}
          >
            <Ionicons name="crop-outline" size={24} color={activeTab === 'crop' ? "#4fc3f7" : "#ffffff"} />
            <Text style={[styles.tabText, activeTab === 'crop' && styles.activeTabText]}>Crop</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'rotate' && styles.activeTab]}
            onPress={() => setActiveTab('rotate')}
          >
            <Ionicons name="refresh-outline" size={24} color={activeTab === 'rotate' ? "#4fc3f7" : "#ffffff"} />
            <Text style={[styles.tabText, activeTab === 'rotate' && styles.activeTabText]}>Rotate</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'flip' && styles.activeTab]}
            onPress={() => setActiveTab('flip')}
          >
            <Ionicons name="swap-horizontal-outline" size={24} color={activeTab === 'flip' ? "#4fc3f7" : "#ffffff"} />
            <Text style={[styles.tabText, activeTab === 'flip' && styles.activeTabText]}>Flip</Text>
          </TouchableOpacity>
        </View>

        {/* Footer with editing tools based on active tab */}
        <View style={styles.footer}>
          {activeTab === 'crop' && (
            <View style={styles.toolsContainer}>
              <View style={styles.cropMessageContainer}>
                <Text style={styles.footerText}>
                  Drag to adjust the crop area
                </Text>

                {/* Manual crop message */}
                <View style={styles.manualCropContainer}>
                  <TouchableOpacity
                    style={[
                      styles.aspectRatioButton,
                      styles.activeAspectRatioButton
                    ]}
                  >
                    <Ionicons
                      name="crop-outline"
                      size={20}
                      color="#4fc3f7"
                    />
                    <Text style={[
                      styles.aspectRatioText,
                      styles.activeAspectRatioText
                    ]}>Manual Crop</Text>
                  </TouchableOpacity>
                </View>

                <TouchableOpacity
                  style={styles.cropButton}
                  onPress={previewTransformations}
                >
                  <Ionicons name="checkmark-circle" size={24} color="#ffffff" />
                  <Text style={styles.cropButtonText}>Apply Crop</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}

          {activeTab === 'rotate' && (
            <View style={styles.toolsContainer}>
              <TouchableOpacity style={styles.toolButton} onPress={rotateLeft}>
                <Ionicons name="arrow-undo" size={28} color="#4fc3f7" />
                <Text style={styles.toolButtonText}>Rotate Left</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.toolButton} onPress={rotateRight}>
                <Ionicons name="arrow-redo" size={28} color="#4fc3f7" />
                <Text style={styles.toolButtonText}>Rotate Right</Text>
              </TouchableOpacity>
            </View>
          )}

          {activeTab === 'flip' && (
            <View style={styles.toolsContainer}>
              <TouchableOpacity
                style={[styles.toolButton, flipHorizontal && styles.activeToolButton]}
                onPress={toggleFlipHorizontal}
              >
                <Ionicons name="swap-horizontal" size={28} color={flipHorizontal ? "#4fc3f7" : "#bbbbbb"} />
                <Text style={[styles.toolButtonText, flipHorizontal && styles.activeToolButtonText]}>Flip Horizontal</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.toolButton, flipVertical && styles.activeToolButton]}
                onPress={toggleFlipVertical}
              >
                <Ionicons name="swap-vertical" size={28} color={flipVertical ? "#4fc3f7" : "#bbbbbb"} />
                <Text style={[styles.toolButtonText, flipVertical && styles.activeToolButtonText]}>Flip Vertical</Text>
              </TouchableOpacity>
            </View>
          )}

          {/* Preview button - only show when changes have been made but not previewed */}
          {(rotation !== 0 || flipHorizontal || flipVertical) && !editedImageUri && activeTab !== 'crop' && (
            <TouchableOpacity style={styles.previewButton} onPress={previewTransformations}>
              <Text style={styles.previewButtonText}>Apply Changes</Text>
            </TouchableOpacity>
          )}
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#151515',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 50 : 20,
    paddingBottom: 15,
    backgroundColor: '#1a1a1a',
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  headerButton: {
    padding: 10,
    backgroundColor: '#252525',
    borderRadius: 5,
    paddingHorizontal: 12,
  },
  headerButtonText: {
    color: '#4fc3f7',
    fontSize: 17,
  },
  doneButton: {
    fontWeight: 'bold',
  },
  headerTitle: {
    color: '#ffffff',
    fontSize: 17,
    fontWeight: 'bold',
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#101010',
  },
  image: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT * 0.5,
  },
  cropOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.65)',
  },
  cropGuide: {
    width: '80%',
    height: '80%',
    borderWidth: 2,
    borderColor: '#4fc3f7',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    position: 'relative',
  },

  cropCorner: {
    position: 'absolute',
    top: -10,
    left: -10,
    width: 20,
    height: 20,
    borderColor: '#4fc3f7',
    borderTopWidth: 3,
    borderLeftWidth: 3,
    backgroundColor: 'rgba(79, 195, 247, 0.3)',
    borderRadius: 3,
  },
  topRight: {
    left: 'auto',
    right: -10,
    borderLeftWidth: 0,
    borderRightWidth: 3,
  },
  bottomLeft: {
    top: 'auto',
    bottom: -10,
    borderTopWidth: 0,
    borderBottomWidth: 3,
  },
  bottomRight: {
    top: 'auto',
    left: 'auto',
    right: -10,
    bottom: -10,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    borderRightWidth: 3,
    borderBottomWidth: 3,
  },
  // Grid lines for the crop box
  gridLine: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  horizontalGridLine: {
    width: '100%',
    height: 1,
    top: '33.33%',
    left: 0,
    right: 0,
    bottom: 'auto',
  },
  verticalGridLine: {
    width: 1,
    height: '100%',
    left: '33.33%',
    top: 0,
    bottom: 0,
    right: 'auto',
  },
  // Tabs container
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: '#1a1a1a',
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#333333',
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  activeTab: {
    borderBottomWidth: 3,
    borderBottomColor: '#4fc3f7',
    backgroundColor: '#252525',
  },
  tabText: {
    color: '#ffffff',
    marginLeft: 5,
    fontSize: 15,
  },
  activeTabText: {
    color: '#4fc3f7',
    fontWeight: 'bold',
  },
  // Footer and tools
  footer: {
    padding: 20,
    backgroundColor: '#1a1a1a',
    borderTopWidth: 1,
    borderTopColor: '#333333',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  footerText: {
    color: '#bbbbbb',
    textAlign: 'center',
    fontSize: 15,
  },
  toolsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 10,
  },
  cropMessageContainer: {
    width: '100%',
    alignItems: 'center',
  },
  aspectRatioContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginTop: 15,
    marginBottom: 10,
  },
  manualCropContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    marginTop: 15,
    marginBottom: 10,
  },
  aspectRatioButton: {
    alignItems: 'center',
    padding: 8,
    borderRadius: 5,
    backgroundColor: '#252525',
    minWidth: 60,
  },
  activeAspectRatioButton: {
    backgroundColor: '#2a3a45',
    borderWidth: 1,
    borderColor: '#4fc3f7',
  },
  aspectRatioText: {
    color: '#bbbbbb',
    fontSize: 12,
    marginTop: 4,
  },
  activeAspectRatioText: {
    color: '#4fc3f7',
    fontWeight: 'bold',
  },
  cropButton: {
    backgroundColor: '#4fc3f7',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 5,
    marginTop: 15,
  },
  cropButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  toolButton: {
    alignItems: 'center',
    padding: 10,
    borderRadius: 5,
    backgroundColor: '#252525',
    minWidth: 120,
  },
  activeToolButton: {
    backgroundColor: '#2a3a45',
    borderWidth: 1,
    borderColor: '#4fc3f7',
  },
  toolButtonText: {
    color: '#bbbbbb',
    marginTop: 5,
    fontSize: 14,
  },
  activeToolButtonText: {
    color: '#4fc3f7',
    fontWeight: 'bold',
  },
  previewButton: {
    backgroundColor: '#4fc3f7',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 5,
    alignItems: 'center',
    marginTop: 15,
    alignSelf: 'center',
    minWidth: 180,
  },
  previewButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default SimpleImageEditor;

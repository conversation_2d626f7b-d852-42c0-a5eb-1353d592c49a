{"logs": [{"outputFile": "com.synaptix.app-mergeDebugResources-55:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d29e10314c2a4b4c7355c13794550e06\\transformed\\play-services-base-18.0.1\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2300", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,59,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2299,2379"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4758,4864,5044,5174,5283,5454,5587,5708,5982,6177,6289,6474,6610,6770,6949,7022,7086", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,63,83", "endOffsets": "4859,5039,5169,5278,5449,5582,5703,5816,6172,6284,6469,6605,6765,6944,7017,7081,7165"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\97ed3adf425b530d685cdd8470e3d9b5\\transformed\\core-1.13.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "39,40,41,42,43,44,45,169", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3648,3746,3848,3947,4049,4153,4257,16146", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "3741,3843,3942,4044,4148,4252,4366,16242"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9a19d447f99dacc987e9045d6bd5cdca\\transformed\\browser-1.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "69,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "7288,7715,7817,7936", "endColumns": "106,101,118,104", "endOffsets": "7390,7812,7931,8036"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ef88a174ce7b920cee45b0a5e12a3fac\\transformed\\play-services-basement-18.3.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5821", "endColumns": "160", "endOffsets": "5977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f64dc84a821f94336e410d15a520d5cf\\transformed\\appcompat-1.7.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,551,661,748,854,984,1069,1149,1240,1333,1431,1526,1626,1719,1812,1907,1998,2089,2175,2285,2396,2499,2610,2718,2825,2984,15147", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,546,656,743,849,979,1064,1144,1235,1328,1426,1521,1621,1714,1807,1902,1993,2084,2170,2280,2391,2494,2605,2713,2820,2979,3078,15229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ed22a0039ae602719136455601f8f0f\\transformed\\react-android-0.79.4-debug\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,205,275,358,425,504,585,675,767,838,926,1021,1112,1192,1272,1355,1432,1505,1593,1665,1748,1821", "endColumns": "69,79,69,82,66,78,80,89,91,70,87,94,90,79,79,82,76,72,87,71,82,72,79", "endOffsets": "120,200,270,353,420,499,580,670,762,833,921,1016,1107,1187,1267,1350,1427,1500,1588,1660,1743,1816,1896"}, "to": {"startLines": "33,49,87,89,91,105,106,153,154,155,156,161,162,163,164,165,166,167,168,170,171,172,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3083,4678,9464,9614,9759,10819,10898,14806,14896,14988,15059,15479,15574,15665,15745,15825,15908,15985,16058,16247,16319,16402,16475", "endColumns": "69,79,69,82,66,78,80,89,91,70,87,94,90,79,79,82,76,72,87,71,82,72,79", "endOffsets": "3148,4753,9529,9692,9821,10893,10974,14891,14983,15054,15142,15569,15660,15740,15820,15903,15980,16053,16141,16314,16397,16470,16550"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6550f91160cccf4761958ba318c6d009\\transformed\\material-1.12.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,390,498,580,681,778,878,1000,1085,1150,1216,1313,1393,1455,1547,1614,1688,1749,1828,1892,1946,2062,2121,2183,2237,2319,2448,2540,2615,2710,2791,2875,3019,3098,3179,3326,3419,3498,3553,3604,3670,3749,3830,3901,3981,4053,4131,4206,4278,4389,4486,4563,4661,4759,4837,4918,5018,5075,5159,5225,5308,5395,5457,5521,5584,5660,5762,5869,5966,6072,6131,6186,6275,6362,6439", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,106,107,81,100,96,99,121,84,64,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88,86,76,80", "endOffsets": "278,385,493,575,676,773,873,995,1080,1145,1211,1308,1388,1450,1542,1609,1683,1744,1823,1887,1941,2057,2116,2178,2232,2314,2443,2535,2610,2705,2786,2870,3014,3093,3174,3321,3414,3493,3548,3599,3665,3744,3825,3896,3976,4048,4126,4201,4273,4384,4481,4558,4656,4754,4832,4913,5013,5070,5154,5220,5303,5390,5452,5516,5579,5655,5757,5864,5961,6067,6126,6181,6270,6357,6434,6515"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,71,72,73,88,90,92,93,94,95,96,97,98,99,100,101,102,103,104,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3153,3260,3368,3450,3551,4371,4471,4593,7487,7552,7618,9534,9697,9826,9918,9985,10059,10120,10199,10263,10317,10433,10492,10554,10608,10690,10979,11071,11146,11241,11322,11406,11550,11629,11710,11857,11950,12029,12084,12135,12201,12280,12361,12432,12512,12584,12662,12737,12809,12920,13017,13094,13192,13290,13368,13449,13549,13606,13690,13756,13839,13926,13988,14052,14115,14191,14293,14400,14497,14603,14662,14717,15234,15321,15398", "endLines": "5,34,35,36,37,38,46,47,48,71,72,73,88,90,92,93,94,95,96,97,98,99,100,101,102,103,104,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,158,159,160", "endColumns": "12,106,107,81,100,96,99,121,84,64,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88,86,76,80", "endOffsets": "328,3255,3363,3445,3546,3643,4466,4588,4673,7547,7613,7710,9609,9754,9913,9980,10054,10115,10194,10258,10312,10428,10487,10549,10603,10685,10814,11066,11141,11236,11317,11401,11545,11624,11705,11852,11945,12024,12079,12130,12196,12275,12356,12427,12507,12579,12657,12732,12804,12915,13012,13089,13187,13285,13363,13444,13544,13601,13685,13751,13834,13921,13983,14047,14110,14186,14288,14395,14492,14598,14657,14712,14801,15316,15393,15474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ac4000ff40bdee0ae650966147dfc40c\\transformed\\biometric-1.1.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,265,386,549,703,837,968,1148,1251,1385,1548", "endColumns": "117,91,120,162,153,133,130,179,102,133,162,139", "endOffsets": "168,260,381,544,698,832,963,1143,1246,1380,1543,1683"}, "to": {"startLines": "68,70,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7170,7395,8041,8162,8325,8479,8613,8744,8924,9027,9161,9324", "endColumns": "117,91,120,162,153,133,130,179,102,133,162,139", "endOffsets": "7283,7482,8157,8320,8474,8608,8739,8919,9022,9156,9319,9459"}}]}]}
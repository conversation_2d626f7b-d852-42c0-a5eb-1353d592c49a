const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

// Get the default config
const defaultConfig = getDefaultConfig(__dirname);

// Add resolution for the 'stream' module and other Node.js modules
defaultConfig.resolver.extraNodeModules = {
  ...defaultConfig.resolver.extraNodeModules,
  stream: require.resolve('stream-browserify'),
  crypto: require.resolve('crypto-browserify'),
  http: require.resolve('@tradle/react-native-http'),
  https: require.resolve('https-browserify'),
  os: require.resolve('os-browserify/browser.js'),
  path: require.resolve('path-browserify'),
  fs: require.resolve('react-native-level-fs'),
  net: require.resolve('react-native-tcp'),
  tls: require.resolve('react-native-tcp'),
  zlib: require.resolve('browserify-zlib'),
};

// Add additional asset extensions
defaultConfig.resolver.assetExts = [...defaultConfig.resolver.assetExts, 'db', 'sqlite'];

// Add support for mjs files
defaultConfig.resolver.sourceExts = [...defaultConfig.resolver.sourceExts, 'mjs'];

module.exports = defaultConfig;

# EAS Build Guide for Synaptix

This guide provides step-by-step instructions for building your Synaptix app using EAS Build.

## Prerequisites

Before you begin, make sure you have:

1. **Node.js and npm** installed
2. **EAS CLI** installed: `npm install -g eas-cli`
3. **Expo account** created and logged in: `eas login`
4. **Android Studio** (for Android builds) or **Xcode** (for iOS builds, Mac only)

## Step 1: Fix Permissions

Run the permission fix script to ensure EAS can access all necessary files:

1. Double-click `fix-permissions.bat` or run PowerShell as Administrator and execute:
   ```powershell
   Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass
   .\fix-permissions.ps1
   ```

2. Review the output to ensure all checks passed.

## Step 2: Configure Your Build

1. **Verify app.json**
   
   Make sure your app.json has the correct configuration:
   - Valid package name for Android: `com.synaptix.app`
   - Valid bundleIdentifier for iOS: `com.synaptix.app`
   - Required permissions for Bluetooth functionality

2. **Verify eas.json**
   
   Ensure your eas.json has the correct build profiles:
   - `development`: For development builds with the Expo dev client
   - `preview`: For internal testing
   - `production`: For production builds

## Step 3: Build Your App

### For Android Development Build

```bash
npx eas build --platform android --profile development
```

### For iOS Development Build

```bash
npx eas build --platform ios --profile development
```

### For Production Builds

```bash
# Android
npx eas build --platform android --profile production

# iOS
npx eas build --platform ios --profile production
```

## Step 4: Install the Build

1. **For Android**
   - EAS will provide a QR code or link to download the APK/AAB
   - Scan the QR code or download and install the APK on your device

2. **For iOS**
   - EAS will provide a link to install the app via TestFlight
   - Follow the TestFlight invitation to install the app

## Common Issues and Solutions

### Build Fails with Permission Errors

- Run the `fix-permissions.bat` script as Administrator
- Check the output for any files that couldn't be processed

### "Unable to resolve module" Errors

- Make sure all dependencies are installed: `npm install`
- Clear the Metro bundler cache: `npx expo start --clear`

### Android Build Fails

- Verify your Android package name in app.json
- Ensure all required permissions are listed in app.json
- Check that your eas.json has the correct Android configuration

### iOS Build Fails

- Verify your iOS bundleIdentifier in app.json
- Ensure you have an Apple Developer account connected to EAS
- Check that your eas.json has the correct iOS configuration

## Additional Resources

- [Expo EAS Build Documentation](https://docs.expo.dev/build/introduction/)
- [Troubleshooting EAS Build](https://docs.expo.dev/build/troubleshooting/)
- [README_PERMISSIONS_FIX.md](./README_PERMISSIONS_FIX.md) for detailed permission fixing instructions

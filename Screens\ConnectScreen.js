import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  Image,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import CustomHeader from '../components/CustomHeader';
import GreyButton from '../components/GreyButton';
import DeviceItem from '../components/DeviceItem';
import { useSettings } from '../contexts/SettingsContext';
import { useBluetooth } from '../contexts/BluetoothContext';

export default function ConnectScreen() {
  const navigation = useNavigation();
  const { darkModeEnabled } = useSettings();

  // Get Bluetooth context
  const {
    isBluetoothEnabled,
    permissionsGranted,
    isScanning,
    devices,
    connectedDevice,
    isConnecting,
    deviceStatus,
    startScan,
    stopScan,
    connectToDevice,
  } = useBluetooth();

  // Selected device state
  const [selectedDeviceId, setSelectedDeviceId] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('');

  // Function to handle device selection
  const handleDeviceSelect = (deviceId) => {
    setSelectedDeviceId(deviceId);
  };

  // Function to start scanning for devices
  const handleScanForDevices = () => {
    // Check if Bluetooth is enabled and permissions are granted
    if (!isBluetoothEnabled) {
      Alert.alert(
        'Bluetooth Disabled',
        'Please enable Bluetooth to scan for devices.',
        [{ text: 'OK' }]
      );
      return;
    }

    if (!permissionsGranted) {
      Alert.alert(
        'Permissions Required',
        'Bluetooth permissions are required to scan for devices.',
        [{ text: 'OK' }]
      );
      return;
    }

    // Start scanning using BluetoothContext
    startScan();
  };

  // Function to handle device connection
  const handleConnect = async () => {
    if (!selectedDeviceId) {
      Alert.alert('No Device Selected', 'Please select a device to connect.');
      return;
    }

    const selectedDevice = devices.find(device => device.id === selectedDeviceId);
    if (!selectedDevice) {
      Alert.alert('Device Not Found', 'Selected device is no longer available.');
      return;
    }

    // Check if already connected to this device
    if (connectedDevice && connectedDevice.id === selectedDevice.id) {
      Alert.alert(
        'Already Connected',
        'You are already connected to this device.',
        [
          {
            text: 'Go to Results',
            onPress: () => navigation.navigate('Result')
          },
          { text: 'OK', style: 'cancel' }
        ]
      );
      return;
    }

    try {
      setConnectionStatus('Connecting...');

      // Show connection attempt message
      Alert.alert(
        'Connecting',
        `Attempting to connect to ${selectedDevice.name || 'device'}...`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => {
              setConnectionStatus('');
              // Note: We could add a cancel connection function here if needed
            }
          }
        ]
      );

      await connectToDevice(selectedDevice);

    } catch (error) {
      console.error('Connection error:', error);
      setConnectionStatus('Connection failed');

      // Provide more specific error messages
      let errorMessage = 'Failed to connect to the device. Please try again.';
      if (error.message) {
        if (error.message.includes('timeout')) {
          errorMessage = 'Connection timed out. Make sure the device is nearby and try again.';
        } else if (error.message.includes('permission')) {
          errorMessage = 'Bluetooth permissions are required to connect to devices.';
        } else if (error.message.includes('disabled')) {
          errorMessage = 'Bluetooth is disabled. Please enable it and try again.';
        }
      }

      Alert.alert(
        'Connection Failed',
        errorMessage,
        [
          {
            text: 'Retry',
            onPress: () => handleConnect()
          },
          { text: 'Cancel', style: 'cancel' }
        ]
      );
    }
  };

  // Effect to handle connection status changes
  useEffect(() => {
    if (deviceStatus === 'connected' && connectedDevice) {
      setConnectionStatus('Connected successfully!');

      // Show success alert and navigate
      Alert.alert(
        'Connection Successful',
        `Successfully connected to ${connectedDevice.name || 'device'}. You can now view real-time EEG data.`,
        [
          {
            text: 'View Results',
            onPress: () => navigation.navigate('Result')
          }
        ]
      );

    } else if (deviceStatus === 'streaming' && connectedDevice) {
      setConnectionStatus('Receiving EEG data...');

    } else if (deviceStatus === 'disconnected' && connectionStatus && connectedDevice === null) {
      setConnectionStatus('Device disconnected');

      // Show disconnection alert
      Alert.alert(
        'Device Disconnected',
        'The device has been disconnected. You may need to reconnect to continue receiving data.',
        [{ text: 'OK' }]
      );

      // Clear status after showing alert
      setTimeout(() => {
        setConnectionStatus('');
      }, 3000);
    }
  }, [deviceStatus, connectedDevice, navigation, connectionStatus]);

  // Effect to handle connection state changes
  useEffect(() => {
    if (isConnecting) {
      setConnectionStatus('Connecting...');
    }
  }, [isConnecting]);

  return (
    <View style={[
      styles.container,
      { backgroundColor: darkModeEnabled ? '#121212' : '#f5f5f5' }
    ]}>
      <StatusBar barStyle={darkModeEnabled ? "light-content" : "dark-content"} />
      <CustomHeader
        title="Connect"
        showProfileIcon={false} />

      {/* Bluetooth Status Banner */}
      {(!isBluetoothEnabled || !permissionsGranted) && (
        <View style={[
          styles.statusBanner,
          { backgroundColor: darkModeEnabled ? '#2d1b69' : '#e8eaf6' }
        ]}>
          <Ionicons
            name="warning-outline"
            size={20}
            color={darkModeEnabled ? '#bb86fc' : '#5e35b1'}
          />
          <Text style={[
            styles.statusBannerText,
            { color: darkModeEnabled ? '#bb86fc' : '#5e35b1' }
          ]}>
            {!isBluetoothEnabled
              ? 'Bluetooth is disabled. Please enable it to scan for devices.'
              : 'Bluetooth permissions required to scan for devices.'
            }
          </Text>
        </View>
      )}

      <View style={styles.imageContainer}>
        <Image
          source={require('../assets/brain.png')}
          style={styles.brainImage}
          resizeMode="contain"
        />
      </View>

      <View style={styles.deviceSelectionContainer}>
        <View style={styles.deviceListHeader}>
          <View style={styles.deviceHeaderInfo}>
            <Text style={[
              styles.deviceSelectionLabel,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>Available Devices</Text>
            {devices.length > 0 && (
              <Text style={[
                styles.deviceCount,
                { color: darkModeEnabled ? '#bb86fc' : '#9C27B0' }
              ]}>
                {devices.length} found
              </Text>
            )}
          </View>

          <TouchableOpacity
            style={styles.refreshButton}
            onPress={handleScanForDevices}
            disabled={isScanning || !isBluetoothEnabled || !permissionsGranted}
          >
            {isScanning ? (
              <ActivityIndicator size="small" color="#9C27B0" />
            ) : (
              <Ionicons
                name="refresh-outline"
                size={22}
                color={(!isBluetoothEnabled || !permissionsGranted) ? '#9e9e9e' : '#9C27B0'}
              />
            )}
          </TouchableOpacity>
        </View>

        <View style={[
          styles.deviceListContainer,
          {
            backgroundColor: darkModeEnabled
              ? 'rgba(30, 30, 30, 0.7)'
              : 'rgba(255, 255, 255, 0.7)',
            borderColor: darkModeEnabled
              ? 'rgba(100, 100, 100, 0.5)'
              : 'rgba(200, 200, 200, 0.8)',
            shadowColor: darkModeEnabled ? '#000000' : '#000000',
            shadowOpacity: darkModeEnabled ? 0.3 : 0.2,
            shadowOffset: { width: 0, height: 2 },
            shadowRadius: 4,
            elevation: 4
          }
        ]}>
          {devices.length > 0 ? (
            <FlatList
              data={devices}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <DeviceItem
                  device={item}
                  selected={selectedDeviceId === item.id}
                  onSelect={handleDeviceSelect}
                />
              )}
              contentContainerStyle={styles.deviceList}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <View style={styles.noDevicesContainer}>
              {isScanning ? (
                <>
                  <ActivityIndicator size="large" color="#9C27B0" />
                  <Text style={[
                    styles.noDevicesText,
                    {
                      color: darkModeEnabled ? '#ffffff' : '#333333',
                      textShadowColor: darkModeEnabled ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 0, 0, 0.2)',
                      textShadowOffset: { width: 1, height: 1 },
                      textShadowRadius: 2
                    }
                  ]}>
                    Scanning for Bluetooth devices...
                  </Text>
                </>
              ) : (
                <>
                  <Ionicons
                    name="bluetooth-outline"
                    size={40}
                    color={darkModeEnabled ? '#9e9e9e' : '#757575'}
                  />
                  <Text style={[
                    styles.noDevicesText,
                    {
                      color: darkModeEnabled ? '#ffffff' : '#333333',
                      textShadowColor: darkModeEnabled ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 0, 0, 0.2)',
                      textShadowOffset: { width: 1, height: 1 },
                      textShadowRadius: 2
                    }
                  ]}>
                    {!isBluetoothEnabled || !permissionsGranted
                      ? 'Enable Bluetooth and grant permissions to scan for devices.'
                      : 'No devices found. Tap the refresh button to scan again.'
                    }
                  </Text>
                </>
              )}
            </View>
          )}
        </View>
      </View>

      {/* Connection Status */}
      {connectionStatus ? (
        <View style={styles.statusContainer}>
          <Text style={[
            styles.statusText,
            { color: darkModeEnabled ? '#ffffff' : '#333333' }
          ]}>
            {connectionStatus}
          </Text>
          {isConnecting && (
            <ActivityIndicator
              size="small"
              color="#9C27B0"
              style={styles.statusIndicator}
            />
          )}
        </View>
      ) : null}

      <GreyButton
        text={isConnecting ? "CONNECTING..." : "CONNECT"}
        onPress={handleConnect}
        style={styles.connectButton}
        disabled={!selectedDeviceId || isConnecting || !isBluetoothEnabled || !permissionsGranted}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  imageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  brainImage: {
    width: 120,
    height: 120,
  },

  deviceSelectionContainer: {
    marginTop: 20,
    marginBottom: 20,
    flex: 1,
  },
  deviceListHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  deviceHeaderInfo: {
    flex: 1,
  },
  deviceCount: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 2,
  },
  deviceListTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    flex: 1,
  },
  deviceSelectionLabel: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(156, 39, 176, 0.1)',
  },
  deviceListContainer: {
    flex: 1,
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
    marginTop: 5,
    marginBottom: 5,
    // Add a subtle glass-like effect
    backdropFilter: 'blur(8px)',
    // For Android, we can't use backdropFilter, so we rely on the semi-transparent background
  },
  deviceList: {
    padding: 10,
  },
  noDevicesContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  noDevicesText: {
    padding: 20,
    textAlign: 'center',
    fontSize: 15,
    fontWeight: '500',
    marginTop: 10,
  },
  connectButton: {
    marginVertical: 20,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10,
    paddingHorizontal: 20,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  statusIndicator: {
    marginLeft: 10,
  },
  statusBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(94, 53, 177, 0.3)',
  },
  statusBannerText: {
    flex: 1,
    marginLeft: 10,
    fontSize: 14,
    fontWeight: '500',
  },
});

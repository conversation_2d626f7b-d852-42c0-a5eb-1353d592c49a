import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Image,
  FlatList,
  Pressable,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function AccountSwitcher({
  visible,
  onClose,
  accounts = [],
  onSelectAccount,
  onAddAccount,
  currentUserEmail,
}) {
  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <View style={styles.header}>
            <Text style={styles.headerText}>Switch Accounts</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Ionicons name="close" size={24} color="#e0e0e0" />
            </TouchableOpacity>
          </View>

          <FlatList
            data={accounts}
            keyExtractor={(item) => item.email}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[
                  styles.accountItem,
                  currentUserEmail === item.email && styles.currentAccountItem
                ]}
                onPress={() => onSelectAccount(item)}
                disabled={currentUserEmail === item.email}
              >
                {item.profilePicture ? (
                  <Image
                    source={{ uri: item.profilePicture }}
                    style={styles.accountImage}
                  />
                ) : (
                  <View style={styles.accountImagePlaceholder}>
                    <Text style={styles.accountImagePlaceholderText}>
                      {item.name ? item.name.charAt(0).toUpperCase() : '?'}
                    </Text>
                  </View>
                )}
                <View style={styles.accountInfo}>
                  <Text style={styles.accountName}>{item.name || 'User'}</Text>
                  <Text style={styles.accountEmail}>{item.email}</Text>
                </View>
                {currentUserEmail === item.email && (
                  <Ionicons name="checkmark-circle" size={24} color="#ffffff" />
                )}
              </TouchableOpacity>
            )}
            ListFooterComponent={
              <TouchableOpacity
                style={styles.addAccountButton}
                onPress={onAddAccount}
              >
                <View style={styles.addAccountIcon}>
                  <Ionicons name="add" size={24} color="#ffffff" />
                </View>
                <Text style={styles.addAccountText}>Add Another Account</Text>
              </TouchableOpacity>
            }
          />
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    backgroundColor: '#1e1e1e',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 5,
  },
  accountItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  currentAccountItem: {
    backgroundColor: '#333333',
    borderRadius: 8,
    paddingHorizontal: 10,
  },
  accountImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  accountImagePlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  accountImagePlaceholderText: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  accountInfo: {
    flex: 1,
  },
  accountName: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  accountEmail: {
    color: '#e0e0e0',
    fontSize: 14,
  },
  addAccountButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    marginTop: 10,
  },
  addAccountIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  addAccountText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

import { createRef } from 'react';

export const navigationRef = createRef();

export function navigate(name, params) {
  if (navigationRef.current) {
    navigationRef.current.navigate(name, params);
  } else {
    console.log('Navigation ref is not set');
  }
}

export function goBack() {
  if (navigationRef.current) {
    navigationRef.current.goBack();
  } else {
    console.log('Navigation ref is not set');
  }
}

export function reset(state) {
  if (navigationRef.current) {
    navigationRef.current.reset(state);
  } else {
    console.log('Navigation ref is not set');
  }
}

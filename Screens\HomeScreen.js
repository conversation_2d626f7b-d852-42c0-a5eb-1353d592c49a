import React, { useState } from 'react';
import { View, Text, StyleSheet, StatusBar, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import CustomHeader from '../components/CustomHeader';
import ProfilePopupMenu from '../components/ProfilePopupMenu';
import SimpleEEGAnimation from '../components/SimpleEEGAnimation';
import { useNavigation } from '@react-navigation/native';
import { useSettings } from '../contexts/SettingsContext';

export default function HomeScreen() {
  const navigation = useNavigation();
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const { darkModeEnabled } = useSettings();

  const handleProfilePress = () => {
    setShowProfileMenu(true);
  };

  const handleCloseMenu = () => {
    setShowProfileMenu(false);
  };

  const handleProfileNavigate = () => {
    setShowProfileMenu(false);
    navigation.navigate('UserProfile');
  };

  const handleSettingsNavigate = () => {
    setShowProfileMenu(false);
    navigation.navigate('HelpCenter');
  };

  const handleUserGuidePress = () => {
    navigation.navigate('UserGuideTab');
  };

  const handleHistoryPress = () => {
    navigation.navigate('History');
  };

  return (
    <View style={[
      styles.container,
      { backgroundColor: darkModeEnabled ? '#121212' : '#f5f5f5' }
    ]}>
      <StatusBar
        barStyle={darkModeEnabled ? "light-content" : "dark-content"}
        backgroundColor={darkModeEnabled ? "#121212" : "#f5f5f5"}
        translucent={false}
      />
      <CustomHeader
        title=""
        onProfilePress={handleProfilePress}
        showProfileIcon={true}
        showLogo={true} />

      {/* Profile Popup Menu - Modal implementation */}
      <ProfilePopupMenu
        visible={showProfileMenu}
        onClose={handleCloseMenu}
        onProfilePress={handleProfileNavigate}
        onSettingsPress={handleSettingsNavigate}
      />

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 20 }}>
        <View style={styles.imageContainer}>
          <View style={styles.eegContainer}>
            <SimpleEEGAnimation />
          </View>

          {/* Quick Access Icons */}
          <View style={styles.quickAccessContainer}>
            <TouchableOpacity
              style={[
                styles.quickAccessButton,
                { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
              ]}
              onPress={handleUserGuidePress}
            >
              <Ionicons
                name="newspaper-outline"
                size={24}
                color={darkModeEnabled ? "#cccccc" : "#666666"}
              />
              <Text style={[
                styles.quickAccessText,
                { color: darkModeEnabled ? '#cccccc' : '#666666' }
              ]}>User Guide</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.quickAccessButton,
                { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
              ]}
              onPress={handleHistoryPress}
            >
              <Ionicons
                name="time-outline"
                size={24}
                color={darkModeEnabled ? "#cccccc" : "#666666"}
              />
              <Text style={[
                styles.quickAccessText,
                { color: darkModeEnabled ? '#cccccc' : '#666666' }
              ]}>History</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={[
          styles.featuresContainer,
          { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
        ]}>
          <Text style={[
            styles.featuresTitle,
            { color: darkModeEnabled ? '#ffffff' : '#121212' }
          ]}>Features</Text>

          <View style={styles.featureItem}>
            <View style={[
              styles.featureIconPlaceholder,
              { backgroundColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
            ]}>
              <Text style={styles.featureIconText}>🧠</Text>
            </View>
            <View style={styles.featureContent}>
              <Text style={[
                styles.featureName,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>Neural Analysis</Text>
              <Text style={[
                styles.featureDescription,
                { color: darkModeEnabled ? '#9e9e9e' : '#666666' }
              ]}>
                Advanced scanning of brain patterns to detect cognitive function
              </Text>
            </View>
          </View>

          <View style={styles.featureItem}>
            <View style={[
              styles.featureIconPlaceholder,
              { backgroundColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
            ]}>
              <Text style={styles.featureIconText}>🧘</Text>
            </View>
            <View style={styles.featureContent}>
              <Text style={[
                styles.featureName,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>Mindfulness Training</Text>
              <Text style={[
                styles.featureDescription,
                { color: darkModeEnabled ? '#9e9e9e' : '#666666' }
              ]}>
                Guided exercises to improve focus and mental clarity
              </Text>
            </View>
          </View>

          <View style={styles.featureItem}>
            <View style={[
              styles.featureIconPlaceholder,
              { backgroundColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
            ]}>
              <Text style={styles.featureIconText}>💡</Text>
            </View>
            <View style={styles.featureContent}>
              <Text style={[
                styles.featureName,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>Personalized Insights</Text>
              <Text style={[
                styles.featureDescription,
                { color: darkModeEnabled ? '#9e9e9e' : '#666666' }
              ]}>
                Custom recommendations based on your brain's unique patterns
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 0,
    paddingBottom: 20,
    paddingTop: 0,
  },
  imageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 5, // Reduced top margin to move container up
    marginBottom: 20,
    width: '100%',
  },
  eegContainer: {
    width: '100%',
    height: 200, // Reduced height to half the size
    borderRadius: 15,
    marginBottom: 20,
    // Add shadow for better differentiation from background
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    // Add a subtle border
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  infoContainer: {
    borderRadius: 10,
    padding: 20,
    marginBottom: 20,
  },
  infoTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  infoText: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
  },
  featuresContainer: {
    borderRadius: 10,
    padding: 20,
    marginBottom: 30,
  },
  featuresTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  featureItem: {
    flexDirection: 'row',
    marginBottom: 15,
    alignItems: 'center',
  },
  featureIconPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  featureIconText: {
    fontSize: 24,
  },
  featureContent: {
    flex: 1,
  },
  featureName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  featureDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  quickAccessContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 20,
  },
  quickAccessButton: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    width: '48%',
    paddingVertical: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  quickAccessText: {
    marginTop: 8,
    fontSize: 14,
    fontWeight: '500',
  },
});

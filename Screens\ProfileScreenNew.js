import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import { useSettings } from '../contexts/SettingsContext';

export default function ProfileScreen({ navigation }) {
  const { currentUser } = useAuth();

  // Get settings from context
  const {
    // Settings states
    darkModeEnabled,
  } = useSettings();

  // Force re-render when theme changes
  const [, forceUpdate] = useState();
  useEffect(() => {
    forceUpdate({});
  }, [darkModeEnabled]);

  // Format date if it exists
  const formatDate = (dateString) => {
    if (!dateString) return 'Not provided';

    // Check if the date is in MM/DD/YYYY format
    const dateRegex = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
    const match = dateString.match(dateRegex);

    if (match) {
      const month = parseInt(match[1], 10);
      const day = parseInt(match[2], 10);
      const year = parseInt(match[3], 10);

      // Create a more readable format
      const months = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];

      return `${months[month - 1]} ${day}, ${year}`;
    }

    return dateString;
  };

  const renderAccountTab = () => (
    <View style={styles.tabContent}>
      <View style={[
        styles.avatarPlaceholder,
        { backgroundColor: darkModeEnabled ? '#cccccc' : '#666666' }
      ]}>
        <Text style={[
          styles.avatarText,
          { color: darkModeEnabled ? '#333333' : '#ffffff' }
        ]}>
          {currentUser?.name ? currentUser.name.charAt(0).toUpperCase() : '?'}
        </Text>
      </View>

      <Text style={[
        styles.userName,
        { color: darkModeEnabled ? '#ffffff' : '#121212' }
      ]}>{currentUser?.name || 'User'}</Text>

      <View style={[
        styles.infoCard,
        { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
      ]}>
        <View style={[
          styles.sectionHeader,
          { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
        ]}>
          <Text style={[
            styles.sectionTitle,
            { color: darkModeEnabled ? '#ffffff' : '#121212' }
          ]}>Personal Information</Text>
        </View>

        <View style={[
          styles.infoRow,
          { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
        ]}>
          <Text style={[
            styles.infoLabel,
            { color: darkModeEnabled ? '#9e9e9e' : '#666666' }
          ]}>Email</Text>
          <Text style={[
            styles.infoValue,
            { color: darkModeEnabled ? '#ffffff' : '#121212' }
          ]}>{currentUser?.email || 'Not provided'}</Text>
        </View>

        <View style={[
          styles.infoRow,
          { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
        ]}>
          <Text style={[
            styles.infoLabel,
            { color: darkModeEnabled ? '#9e9e9e' : '#666666' }
          ]}>Date of Birth</Text>
          <Text style={[
            styles.infoValue,
            { color: darkModeEnabled ? '#ffffff' : '#121212' }
          ]}>{formatDate(currentUser?.dob)}</Text>
        </View>

        <View style={[
          styles.infoRow,
          { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
        ]}>
          <Text style={[
            styles.infoLabel,
            { color: darkModeEnabled ? '#9e9e9e' : '#666666' }
          ]}>Gender</Text>
          <Text style={[
            styles.infoValue,
            { color: darkModeEnabled ? '#ffffff' : '#121212' }
          ]}>
            {currentUser?.gender
              ? currentUser.gender.charAt(0).toUpperCase() + currentUser.gender.slice(1)
              : 'Not provided'}
          </Text>
        </View>
      </View>

      <TouchableOpacity
        style={[
          styles.editButton,
          { backgroundColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
        ]}
        onPress={() => navigation.navigate('EditProfile')}
      >
        <Text style={[
          styles.editButtonText,
          { color: darkModeEnabled ? '#ffffff' : '#121212' }
        ]}>EDIT PROFILE</Text>
      </TouchableOpacity>
    </View>
  );



  return (
    <View style={[
      styles.container,
      { backgroundColor: darkModeEnabled ? '#121212' : '#f5f5f5' }
    ]}>
      <StatusBar barStyle={darkModeEnabled ? "light-content" : "dark-content"} />
      <View style={[
        styles.header,
        { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
      ]}>
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons
              name="arrow-back"
              size={24}
              color={darkModeEnabled ? "#cccccc" : "#121212"}
            />
          </TouchableOpacity>
          <Text style={[
            styles.title,
            { color: darkModeEnabled ? '#ffffff' : '#121212' }
          ]}>My Profile</Text>
          <View style={styles.placeholderRight} />
        </View>
      </View>

      <ScrollView style={styles.scrollContainer}>
        <View style={styles.profileContainer}>
          {renderAccountTab()}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  placeholderRight: {
    width: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  scrollContainer: {
    flex: 1,
  },
  profileContainer: {
    padding: 20,
  },
  tabContent: {
    alignItems: 'center',
  },
  avatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  avatarText: {
    fontSize: 40,
    fontWeight: 'bold',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
  },
  infoCard: {
    borderRadius: 10,
    padding: 20,
    width: '100%',
    marginBottom: 30,
  },
  sectionHeader: {
    borderBottomWidth: 1,
    paddingBottom: 15,
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 15,
    borderBottomWidth: 1,
  },
  infoLabel: {
    fontSize: 16,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '500',
    maxWidth: '60%',
    textAlign: 'right',
  },
  editButton: {
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 30,
    marginBottom: 20,
  },
  editButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});
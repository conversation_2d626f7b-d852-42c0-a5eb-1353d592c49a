import React, { createContext, useState, useEffect, useContext } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import dataBackupHelper from '../utils/dataBackupHelper';

// Create the settings context
const SettingsContext = createContext();

// Custom hook to use the settings context
export const useSettings = () => useContext(SettingsContext);

// Settings keys for AsyncStorage
const SETTINGS_KEYS = {
  NOTIFICATIONS_ENABLED: 'settings_notifications_enabled',
  DARK_MODE_ENABLED: 'settings_dark_mode_enabled',
  DATA_BACKUP_ENABLED: 'settings_data_backup_enabled',
};

// Provider component that wraps the app and makes settings available to any child component
export function SettingsProvider({ children }) {
  // Settings state
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);
  const [darkModeEnabled, setDarkModeEnabled] = useState(true); // Default to dark mode
  const [dataBackupEnabled, setDataBackupEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Loading states for individual settings
  const [notificationsLoading, setNotificationsLoading] = useState(false);
  const [darkModeLoading, setDarkModeLoading] = useState(false);
  const [dataBackupLoading, setDataBackupLoading] = useState(false);

  // Load settings on app start
  useEffect(() => {
    const loadSettings = async () => {
      try {
        // Load settings from AsyncStorage
        const notificationsValue = await AsyncStorage.getItem(SETTINGS_KEYS.NOTIFICATIONS_ENABLED);
        const darkModeValue = await AsyncStorage.getItem(SETTINGS_KEYS.DARK_MODE_ENABLED);
        const dataBackupValue = await AsyncStorage.getItem(SETTINGS_KEYS.DATA_BACKUP_ENABLED);

        // Set state with loaded values (or defaults if not found)
        setNotificationsEnabled(notificationsValue === 'true');
        setDarkModeEnabled(darkModeValue === null ? true : darkModeValue === 'true'); // Default to true if not set
        setDataBackupEnabled(dataBackupValue === 'true');
      } catch (error) {
        console.error('Error loading settings:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, []);

  // Save settings whenever they change
  useEffect(() => {
    if (!isLoading) {
      const saveSettings = async () => {
        try {
          await AsyncStorage.setItem(SETTINGS_KEYS.NOTIFICATIONS_ENABLED, String(notificationsEnabled));
          await AsyncStorage.setItem(SETTINGS_KEYS.DARK_MODE_ENABLED, String(darkModeEnabled));
          await AsyncStorage.setItem(SETTINGS_KEYS.DATA_BACKUP_ENABLED, String(dataBackupEnabled));
        } catch (error) {
          console.error('Error saving settings:', error);
        }
      };

      saveSettings();
    }
  }, [notificationsEnabled, darkModeEnabled, dataBackupEnabled, isLoading]);

  // Notification permission effect removed

  // Effect for handling data backup
  useEffect(() => {
    if (!isLoading && dataBackupEnabled) {
      // Set up a periodic backup (once per day)
      const backupInterval = setInterval(async () => {
        console.log('Running scheduled data backup...');
        const success = await dataBackupHelper.performBackup();
        if (success) {
          console.log('Scheduled backup completed successfully');

          // Notification code removed
        } else {
          console.error('Scheduled backup failed');
        }
      }, 24 * 60 * 60 * 1000); // 24 hours

      // Run an initial backup
      dataBackupHelper.performBackup();

      // Clean up interval on unmount or when setting changes
      return () => clearInterval(backupInterval);
    }
  }, [dataBackupEnabled, notificationsEnabled, isLoading]);

  // Toggle notifications (simplified - no actual notifications)
  const toggleNotifications = async (value) => {
    try {
      // If already in the requested state or loading, do nothing
      if (notificationsEnabled === value || notificationsLoading) {
        return true;
      }

      // Set loading state
      setNotificationsLoading(true);

      // Update state immediately for better responsiveness
      setNotificationsEnabled(value);

      return true;
    } catch (error) {
      console.error('Error toggling notifications:', error);
      // Revert state on error
      setNotificationsEnabled(!value);
      return false;
    } finally {
      // Always clear loading state
      setNotificationsLoading(false);
    }
  };

  // Toggle dark mode
  const toggleDarkMode = async (value) => {
    try {
      // If already in the requested state or loading, do nothing
      if (darkModeEnabled === value || darkModeLoading) {
        return true;
      }

      // Set loading state
      setDarkModeLoading(true);

      // Update state immediately for better responsiveness
      setDarkModeEnabled(value);

      // Small delay to allow theme transition to complete
      setTimeout(() => {
        setDarkModeLoading(false);
      }, 100);

      return true;
    } catch (error) {
      console.error('Error toggling dark mode:', error);
      return false;
    }
  };

  // Toggle data backup
  const toggleDataBackup = async (value) => {
    try {
      // If already in the requested state or loading, do nothing
      if (dataBackupEnabled === value || dataBackupLoading) {
        return true;
      }

      // Set loading state
      setDataBackupLoading(true);

      // Update state immediately for better responsiveness
      setDataBackupEnabled(value);

      if (value) {
        // Run an immediate backup when enabling
        const success = await dataBackupHelper.performBackup();

        // Notification code removed
      }

      return true;
    } catch (error) {
      console.error('Error toggling data backup:', error);
      // Revert state on error
      setDataBackupEnabled(!value);
      return false;
    } finally {
      // Always clear loading state
      setDataBackupLoading(false);
    }
  };

  // Context value
  const value = {
    // Settings states
    notificationsEnabled,
    darkModeEnabled,
    dataBackupEnabled,

    // Loading states
    isLoading,
    notificationsLoading,
    darkModeLoading,
    dataBackupLoading,

    // Toggle functions
    toggleNotifications,
    toggleDarkMode,
    toggleDataBackup,
  };

  return <SettingsContext.Provider value={value}>{children}</SettingsContext.Provider>;
}

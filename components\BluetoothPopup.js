import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Linking,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSettings } from '../contexts/SettingsContext';

const BluetoothPopup = ({ 
  visible, 
  onClose, 
  isBluetoothEnabled, 
  permissionsGranted, 
  onRequestPermissions,
  onEnableBluetooth 
}) => {
  const { darkModeEnabled } = useSettings();

  const handleEnableBluetooth = () => {
    if (Platform.OS === 'android') {
      Linking.openSettings();
    } else {
      // On iOS, we can't directly open Bluetooth settings
      Linking.openURL('App-Prefs:Bluetooth');
    }
    onEnableBluetooth && onEnableBluetooth();
  };

  const handleRequestPermissions = async () => {
    if (onRequestPermissions) {
      await onRequestPermissions();
    }
  };

  const getTitle = () => {
    if (!isBluetoothEnabled) {
      return 'Bluetooth is Disabled';
    } else if (!permissionsGranted) {
      return 'Bluetooth Permissions Required';
    }
    return 'Bluetooth Setup';
  };

  const getMessage = () => {
    if (!isBluetoothEnabled) {
      return 'Please enable Bluetooth in your device settings to scan for and connect to EEG devices.';
    } else if (!permissionsGranted) {
      return 'Synaptix needs Bluetooth permissions to scan for and connect to EEG devices. This is required for the app to function properly.';
    }
    return 'Bluetooth setup is required to use this feature.';
  };

  const getPrimaryAction = () => {
    if (!isBluetoothEnabled) {
      return {
        text: 'Open Settings',
        onPress: handleEnableBluetooth,
        icon: 'settings-outline'
      };
    } else if (!permissionsGranted) {
      return {
        text: 'Grant Permissions',
        onPress: handleRequestPermissions,
        icon: 'checkmark-circle-outline'
      };
    }
    return null;
  };

  const primaryAction = getPrimaryAction();

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={[
          styles.popup,
          { 
            backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff',
            borderColor: darkModeEnabled ? '#333333' : '#e0e0e0'
          }
        ]}>
          {/* Header */}
          <View style={styles.header}>
            <View style={[
              styles.iconContainer,
              { backgroundColor: darkModeEnabled ? '#2d1b69' : '#e8eaf6' }
            ]}>
              <Ionicons
                name="bluetooth-outline"
                size={32}
                color={darkModeEnabled ? '#bb86fc' : '#5e35b1'}
              />
            </View>
            <Text style={[
              styles.title,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              {getTitle()}
            </Text>
          </View>

          {/* Message */}
          <Text style={[
            styles.message,
            { color: darkModeEnabled ? '#e0e0e0' : '#666666' }
          ]}>
            {getMessage()}
          </Text>

          {/* Status Indicators */}
          <View style={styles.statusContainer}>
            <View style={styles.statusItem}>
              <Ionicons
                name={isBluetoothEnabled ? 'checkmark-circle' : 'close-circle'}
                size={20}
                color={isBluetoothEnabled ? '#4caf50' : '#f44336'}
              />
              <Text style={[
                styles.statusText,
                { 
                  color: darkModeEnabled ? '#e0e0e0' : '#666666',
                  opacity: isBluetoothEnabled ? 1 : 0.7
                }
              ]}>
                Bluetooth {isBluetoothEnabled ? 'Enabled' : 'Disabled'}
              </Text>
            </View>
            
            <View style={styles.statusItem}>
              <Ionicons
                name={permissionsGranted ? 'checkmark-circle' : 'close-circle'}
                size={20}
                color={permissionsGranted ? '#4caf50' : '#f44336'}
              />
              <Text style={[
                styles.statusText,
                { 
                  color: darkModeEnabled ? '#e0e0e0' : '#666666',
                  opacity: permissionsGranted ? 1 : 0.7
                }
              ]}>
                Permissions {permissionsGranted ? 'Granted' : 'Required'}
              </Text>
            </View>
          </View>

          {/* Actions */}
          <View style={styles.actions}>
            {primaryAction && (
              <TouchableOpacity
                style={[
                  styles.primaryButton,
                  { backgroundColor: darkModeEnabled ? '#bb86fc' : '#9c27b0' }
                ]}
                onPress={primaryAction.onPress}
              >
                <Ionicons
                  name={primaryAction.icon}
                  size={18}
                  color="#ffffff"
                  style={styles.buttonIcon}
                />
                <Text style={styles.primaryButtonText}>
                  {primaryAction.text}
                </Text>
              </TouchableOpacity>
            )}
            
            <TouchableOpacity
              style={[
                styles.secondaryButton,
                { 
                  borderColor: darkModeEnabled ? '#666666' : '#cccccc',
                  backgroundColor: 'transparent'
                }
              ]}
              onPress={onClose}
            >
              <Text style={[
                styles.secondaryButtonText,
                { color: darkModeEnabled ? '#e0e0e0' : '#666666' }
              ]}>
                Cancel
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  popup: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 24,
  },
  statusContainer: {
    marginBottom: 24,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusText: {
    fontSize: 14,
    marginLeft: 8,
    fontWeight: '500',
  },
  actions: {
    gap: 12,
  },
  primaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  buttonIcon: {
    marginRight: 8,
  },
  primaryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default BluetoothPopup;

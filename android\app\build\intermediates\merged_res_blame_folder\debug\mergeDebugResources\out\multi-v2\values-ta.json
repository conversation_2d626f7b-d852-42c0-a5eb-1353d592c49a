{"logs": [{"outputFile": "com.synaptix.app-mergeDebugResources-55:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ac4000ff40bdee0ae650966147dfc40c\\transformed\\biometric-1.1.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,267,381,498,629,756,879,1018,1117,1258,1409", "endColumns": "119,91,113,116,130,126,122,138,98,140,150,126", "endOffsets": "170,262,376,493,624,751,874,1013,1112,1253,1404,1531"}, "to": {"startLines": "68,70,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6991,7228,7864,7978,8095,8226,8353,8476,8615,8714,8855,9006", "endColumns": "119,91,113,116,130,126,122,138,98,140,150,126", "endOffsets": "7106,7315,7973,8090,8221,8348,8471,8610,8709,8850,9001,9128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9a19d447f99dacc987e9045d6bd5cdca\\transformed\\browser-1.6.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,274,381", "endColumns": "116,101,106,103", "endOffsets": "167,269,376,480"}, "to": {"startLines": "69,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "7111,7551,7653,7760", "endColumns": "116,101,106,103", "endOffsets": "7223,7648,7755,7859"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6550f91160cccf4761958ba318c6d009\\transformed\\material-1.12.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,360,446,530,633,727,836,954,1038,1097,1161,1269,1337,1398,1506,1573,1659,1717,1801,1868,1922,2045,2107,2170,2224,2312,2440,2526,2618,2721,2813,2895,3027,3107,3188,3344,3433,3517,3574,3626,3692,3777,3865,3936,4016,4085,4162,4242,4310,4425,4524,4607,4699,4793,4867,4953,5047,5097,5180,5246,5331,5418,5481,5546,5609,5678,5786,5884,5982,6079,6140,6196,6282,6374,6457", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,87,85,83,102,93,108,117,83,58,63,107,67,60,107,66,85,57,83,66,53,122,61,62,53,87,127,85,91,102,91,81,131,79,80,155,88,83,56,51,65,84,87,70,79,68,76,79,67,114,98,82,91,93,73,85,93,49,82,65,84,86,62,64,62,68,107,97,97,96,60,55,85,91,82,81", "endOffsets": "267,355,441,525,628,722,831,949,1033,1092,1156,1264,1332,1393,1501,1568,1654,1712,1796,1863,1917,2040,2102,2165,2219,2307,2435,2521,2613,2716,2808,2890,3022,3102,3183,3339,3428,3512,3569,3621,3687,3772,3860,3931,4011,4080,4157,4237,4305,4420,4519,4602,4694,4788,4862,4948,5042,5092,5175,5241,5326,5413,5476,5541,5604,5673,5781,5879,5977,6074,6135,6191,6277,6369,6452,6534"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,71,72,73,88,91,93,94,95,96,97,98,99,100,101,102,103,104,105,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3163,3251,3337,3421,3524,4364,4473,4591,7320,7379,7443,9205,9425,9556,9664,9731,9817,9875,9959,10026,10080,10203,10265,10328,10382,10470,10819,10905,10997,11100,11192,11274,11406,11486,11567,11723,11812,11896,11953,12005,12071,12156,12244,12315,12395,12464,12541,12621,12689,12804,12903,12986,13078,13172,13246,13332,13426,13476,13559,13625,13710,13797,13860,13925,13988,14057,14165,14263,14361,14458,14519,14575,15058,15150,15233", "endLines": "5,34,35,36,37,38,46,47,48,71,72,73,88,91,93,94,95,96,97,98,99,100,101,102,103,104,105,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,160,161,162", "endColumns": "12,87,85,83,102,93,108,117,83,58,63,107,67,60,107,66,85,57,83,66,53,122,61,62,53,87,127,85,91,102,91,81,131,79,80,155,88,83,56,51,65,84,87,70,79,68,76,79,67,114,98,82,91,93,73,85,93,49,82,65,84,86,62,64,62,68,107,97,97,96,60,55,85,91,82,81", "endOffsets": "317,3246,3332,3416,3519,3613,4468,4586,4670,7374,7438,7546,9268,9481,9659,9726,9812,9870,9954,10021,10075,10198,10260,10323,10377,10465,10593,10900,10992,11095,11187,11269,11401,11481,11562,11718,11807,11891,11948,12000,12066,12151,12239,12310,12390,12459,12536,12616,12684,12799,12898,12981,13073,13167,13241,13327,13421,13471,13554,13620,13705,13792,13855,13920,13983,14052,14160,14258,14356,14453,14514,14570,14656,15145,15228,15310"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\97ed3adf425b530d685cdd8470e3d9b5\\transformed\\core-1.13.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "39,40,41,42,43,44,45,171", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3618,3714,3817,3916,4014,4121,4236,15971", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "3709,3812,3911,4009,4116,4231,4359,16067"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d29e10314c2a4b4c7355c13794550e06\\transformed\\play-services-base-18.0.1\\res\\values-ta\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,667,815,939,1048,1145,1321,1424,1573,1704,1854,2006,2064,2123", "endColumns": "100,147,122,101,147,123,108,96,175,102,148,130,149,151,57,58,76", "endOffsets": "293,441,564,666,814,938,1047,1144,1320,1423,1572,1703,1853,2005,2063,2122,2199"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4755,4860,5012,5139,5245,5397,5525,5638,5900,6080,6187,6340,6475,6629,6785,6847,6910", "endColumns": "104,151,126,105,151,127,112,100,179,106,152,134,153,155,61,62,80", "endOffsets": "4855,5007,5134,5240,5392,5520,5633,5734,6075,6182,6335,6470,6624,6780,6842,6905,6986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ef88a174ce7b920cee45b0a5e12a3fac\\transformed\\play-services-basement-18.3.0\\res\\values-ta\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "156", "endOffsets": "351"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5739", "endColumns": "160", "endOffsets": "5895"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f64dc84a821f94336e410d15a520d5cf\\transformed\\appcompat-1.7.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,435,537,652,741,852,973,1052,1128,1226,1326,1421,1515,1622,1722,1824,1918,2016,2114,2195,2303,2406,2505,2621,2724,2829,2986,14976", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "430,532,647,736,847,968,1047,1123,1221,1321,1416,1510,1617,1717,1819,1913,2011,2109,2190,2298,2401,2500,2616,2719,2824,2981,3083,15053"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ed22a0039ae602719136455601f8f0f\\transformed\\react-android-0.79.4-debug\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,210,282,350,434,504,571,647,725,807,887,958,1040,1122,1200,1289,1379,1460,1532,1602,1696,1771,1854,1923", "endColumns": "74,79,71,67,83,69,66,75,77,81,79,70,81,81,77,88,89,80,71,69,93,74,82,68,77", "endOffsets": "125,205,277,345,429,499,566,642,720,802,882,953,1035,1117,1195,1284,1374,1455,1527,1597,1691,1766,1849,1918,1996"}, "to": {"startLines": "33,49,87,89,90,92,106,107,108,155,156,157,158,163,164,165,166,167,168,169,170,172,173,174,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3088,4675,9133,9273,9341,9486,10598,10665,10741,14661,14743,14823,14894,15315,15397,15475,15564,15654,15735,15807,15877,16072,16147,16230,16299", "endColumns": "74,79,71,67,83,69,66,75,77,81,79,70,81,81,77,88,89,80,71,69,93,74,82,68,77", "endOffsets": "3158,4750,9200,9336,9420,9551,10660,10736,10814,14738,14818,14889,14971,15392,15470,15559,15649,15730,15802,15872,15966,16142,16225,16294,16372"}}]}]}
1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.synaptix.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:20:3-75
11-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:20:20-73
12    <!-- Location permissions for Bluetooth scanning -->
13    <uses-permission-sdk-23 android:name="android.permission.ACCESS_COARSE_LOCATION" />
13-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:3:3-85
13-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:3:27-83
14    <uses-permission-sdk-23 android:name="android.permission.ACCESS_FINE_LOCATION" />
14-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:4:3-83
14-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:4:27-81
15
16    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- Bluetooth permissions -->
16-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:5:3-76
16-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:5:20-74
17    <uses-permission
17-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:8:3-65
18        android:name="android.permission.BLUETOOTH"
18-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:8:20-63
19        android:maxSdkVersion="30" />
19-->[com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a389579de9b06cbffd04c629fd2510\transformed\rxandroidble-1.17.2\AndroidManifest.xml:12:9-35
20    <uses-permission
20-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:9:3-71
21        android:name="android.permission.BLUETOOTH_ADMIN"
21-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:9:20-69
22        android:maxSdkVersion="30" /> <!-- Bluetooth permissions for Android 12+ (API level 31+) -->
22-->[com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a389579de9b06cbffd04c629fd2510\transformed\rxandroidble-1.17.2\AndroidManifest.xml:15:9-35
23    <uses-permission
23-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:12:3-137
24        android:name="android.permission.BLUETOOTH_SCAN"
24-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:12:20-68
25        android:usesPermissionFlags="neverForLocation" />
25-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:12:69-115
26    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
26-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:13:3-73
26-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:13:20-71
27    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" /> <!-- Other permissions -->
27-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:14:3-75
27-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:14:20-73
28    <uses-permission android:name="android.permission.INTERNET" />
28-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:17:3-64
28-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:17:20-62
29    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
29-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:18:3-77
29-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:18:20-75
30    <uses-permission android:name="android.permission.RECORD_AUDIO" />
30-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:19:3-68
30-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:19:20-66
31    <uses-permission android:name="android.permission.VIBRATE" />
31-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:21:3-63
31-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:21:20-61
32    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
32-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:22:3-78
32-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:22:20-76
33
34    <queries>
34-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:23:3-29:13
35        <intent>
35-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:24:5-28:14
36            <action android:name="android.intent.action.VIEW" />
36-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:25:7-58
36-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:25:15-56
37
38            <category android:name="android.intent.category.BROWSABLE" />
38-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:26:7-67
38-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:26:17-65
39
40            <data android:scheme="https" />
40-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:27:7-37
40-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:27:13-35
41        </intent>
42
43        <package android:name="host.exp.exponent" />
43-->[:expo-dev-launcher] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
43-->[:expo-dev-launcher] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
44
45        <intent>
45-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:15:9-19:18
46
47            <!-- Required for picking images from the camera roll if targeting API 30 -->
48            <action android:name="android.media.action.IMAGE_CAPTURE" />
48-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:13-73
48-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:21-70
49        </intent>
50        <intent>
50-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:20:9-24:18
51
52            <!-- Required for picking images from the camera if targeting API 30 -->
53            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
53-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:13-80
53-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:21-77
54        </intent>
55        <intent>
55-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:8:9-14:18
56            <action android:name="android.intent.action.GET_CONTENT" />
56-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:13-72
56-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:21-69
57
58            <category android:name="android.intent.category.OPENABLE" />
58-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:13-73
58-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:23-70
59
60            <data android:mimeType="*/*" />
60-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:27:7-37
61        </intent> <!-- Query open documents -->
62        <intent>
62-->[:expo-file-system] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
63            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
63-->[:expo-file-system] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
63-->[:expo-file-system] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
64        </intent>
65    </queries> <!-- Required for picking images from camera directly -->
66    <uses-permission android:name="android.permission.CAMERA" />
66-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:8:5-65
66-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:8:22-62
67    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
67-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac4000ff40bdee0ae650966147dfc40c\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
67-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac4000ff40bdee0ae650966147dfc40c\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
68    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
68-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac4000ff40bdee0ae650966147dfc40c\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
68-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac4000ff40bdee0ae650966147dfc40c\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
69    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
69-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:7:5-81
69-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:7:22-78
70    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
70-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:8:5-77
70-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:8:22-74
71    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
71-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
71-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:22-76
72    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
72-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
72-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:22-65
73    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
73-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
73-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
74
75    <permission
75-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
76        android:name="com.synaptix.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
76-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
77        android:protectionLevel="signature" />
77-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
78
79    <uses-permission android:name="com.synaptix.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
79-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
79-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
80    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
80-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e0a266b44f06ef6d5ad9791001789f\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
80-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e0a266b44f06ef6d5ad9791001789f\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
81    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
82    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
83    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
84    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
85    <!-- for Samsung -->
86    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
86-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
86-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
87    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
87-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
87-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
88    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
88-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
88-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
89    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
89-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
89-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
90    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
91    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
92    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
93    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
94    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
94-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
94-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
95    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
95-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
95-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
96    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
96-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
96-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
97    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
97-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
97-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
98    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
98-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
98-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
99    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
99-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
99-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
100    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
100-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
100-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
101    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
101-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
101-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
102
103    <application
103-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:30:3-47:17
104        android:name="com.synaptix.app.MainApplication"
104-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:30:16-47
105        android:allowBackup="true"
105-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:30:116-142
106        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
106-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
107        android:debuggable="true"
108        android:extractNativeLibs="false"
109        android:icon="@mipmap/ic_launcher"
109-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:30:81-115
110        android:label="@string/app_name"
110-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:30:48-80
111        android:supportsRtl="true"
111-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:30:175-201
112        android:theme="@style/AppTheme"
112-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:30:143-174
113        android:usesCleartextTraffic="true" >
113-->C:\Users\<USER>\Projects\Synaptix\android\app\src\debug\AndroidManifest.xml:6:18-53
114        <meta-data
114-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:31:5-83
115            android:name="expo.modules.updates.ENABLED"
115-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:31:16-59
116            android:value="false" />
116-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:31:60-81
117        <meta-data
117-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:32:5-105
118            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
118-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:32:16-80
119            android:value="ALWAYS" />
119-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:32:81-103
120        <meta-data
120-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:33:5-99
121            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
121-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:33:16-79
122            android:value="0" />
122-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:33:80-97
123
124        <activity
124-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:34:5-46:16
125            android:name="com.synaptix.app.MainActivity"
125-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:34:15-43
126            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
126-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:34:44-134
127            android:exported="true"
127-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:34:256-279
128            android:launchMode="singleTask"
128-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:34:135-166
129            android:screenOrientation="portrait"
129-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:34:280-316
130            android:theme="@style/Theme.App.SplashScreen"
130-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:34:210-255
131            android:windowSoftInputMode="adjustResize" >
131-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:34:167-209
132            <intent-filter>
132-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:35:7-38:23
133                <action android:name="android.intent.action.MAIN" />
133-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:36:9-60
133-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:36:17-58
134
135                <category android:name="android.intent.category.LAUNCHER" />
135-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:37:9-68
135-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:37:19-66
136            </intent-filter>
137            <intent-filter>
137-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:39:7-45:23
138                <action android:name="android.intent.action.VIEW" />
138-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:25:7-58
138-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:25:15-56
139
140                <category android:name="android.intent.category.DEFAULT" />
140-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:41:9-67
140-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:41:19-65
141                <category android:name="android.intent.category.BROWSABLE" />
141-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:26:7-67
141-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:26:17-65
142
143                <data android:scheme="synaptix" />
143-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:27:7-37
143-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:27:13-35
144                <data android:scheme="exp+synaptix" />
144-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:27:7-37
144-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:27:13-35
145            </intent-filter>
146        </activity>
147        <activity
147-->[:expo-dev-launcher] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
148            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
148-->[:expo-dev-launcher] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
149            android:exported="true"
149-->[:expo-dev-launcher] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
150            android:launchMode="singleTask"
150-->[:expo-dev-launcher] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
151            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
151-->[:expo-dev-launcher] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
152            <intent-filter>
152-->[:expo-dev-launcher] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
153                <action android:name="android.intent.action.VIEW" />
153-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:25:7-58
153-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:25:15-56
154
155                <category android:name="android.intent.category.DEFAULT" />
155-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:41:9-67
155-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:41:19-65
156                <category android:name="android.intent.category.BROWSABLE" />
156-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:26:7-67
156-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:26:17-65
157
158                <data android:scheme="expo-dev-launcher" />
158-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:27:7-37
158-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:27:13-35
159            </intent-filter>
160        </activity>
161        <activity
161-->[:expo-dev-launcher] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
162            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
162-->[:expo-dev-launcher] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
163            android:screenOrientation="portrait"
163-->[:expo-dev-launcher] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
164            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
164-->[:expo-dev-launcher] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
165        <activity
165-->[:expo-dev-menu] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
166            android:name="expo.modules.devmenu.DevMenuActivity"
166-->[:expo-dev-menu] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
167            android:exported="true"
167-->[:expo-dev-menu] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
168            android:launchMode="singleTask"
168-->[:expo-dev-menu] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
169            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
169-->[:expo-dev-menu] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
170            <intent-filter>
170-->[:expo-dev-menu] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
171                <action android:name="android.intent.action.VIEW" />
171-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:25:7-58
171-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:25:15-56
172
173                <category android:name="android.intent.category.DEFAULT" />
173-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:41:9-67
173-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:41:19-65
174                <category android:name="android.intent.category.BROWSABLE" />
174-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:26:7-67
174-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:26:17-65
175
176                <data android:scheme="expo-dev-menu" />
176-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:27:7-37
176-->C:\Users\<USER>\Projects\Synaptix\android\app\src\main\AndroidManifest.xml:27:13-35
177            </intent-filter>
178        </activity>
179
180        <meta-data
180-->[:expo-modules-core] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
181            android:name="org.unimodules.core.AppLoader#react-native-headless"
181-->[:expo-modules-core] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
182            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
182-->[:expo-modules-core] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
183        <meta-data
183-->[:expo-modules-core] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
184            android:name="com.facebook.soloader.enabled"
184-->[:expo-modules-core] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
185            android:value="true" />
185-->[:expo-modules-core] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
186
187        <activity
187-->[com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed22a0039ae602719136455601f8f0f\transformed\react-android-0.79.4-debug\AndroidManifest.xml:19:9-21:40
188            android:name="com.facebook.react.devsupport.DevSettingsActivity"
188-->[com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed22a0039ae602719136455601f8f0f\transformed\react-android-0.79.4-debug\AndroidManifest.xml:20:13-77
189            android:exported="false" />
189-->[com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed22a0039ae602719136455601f8f0f\transformed\react-android-0.79.4-debug\AndroidManifest.xml:21:13-37
190
191        <service
191-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:28:9-40:19
192            android:name="com.google.android.gms.metadata.ModuleDependencies"
192-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:29:13-78
193            android:enabled="false"
193-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:30:13-36
194            android:exported="false" >
194-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:31:13-37
195            <intent-filter>
195-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:33:13-35:29
196                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
196-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:17-94
196-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:25-91
197            </intent-filter>
198
199            <meta-data
199-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:37:13-39:36
200                android:name="photopicker_activity:0:required"
200-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:38:17-63
201                android:value="" />
201-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:39:17-33
202        </service>
203
204        <activity
204-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:42:9-44:59
205            android:name="com.canhub.cropper.CropImageActivity"
205-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:43:13-64
206            android:exported="true"
206-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:33:13-36
207            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
207-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:44:13-56
208        <provider
208-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:46:9-54:20
209            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
209-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:47:13-89
210            android:authorities="com.synaptix.app.ImagePickerFileProvider"
210-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:48:13-75
211            android:exported="false"
211-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:49:13-37
212            android:grantUriPermissions="true" >
212-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:50:13-47
213            <meta-data
213-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:51:13-53:71
214                android:name="android.support.FILE_PROVIDER_PATHS"
214-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:52:17-67
215                android:resource="@xml/image_picker_provider_paths" />
215-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:53:17-68
216        </provider>
217        <provider
217-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:21:9-29:20
218            android:name="com.canhub.cropper.CropFileProvider"
218-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:22:13-63
219            android:authorities="com.synaptix.app.cropper.fileprovider"
219-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:23:13-72
220            android:exported="false"
220-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:24:13-37
221            android:grantUriPermissions="true" >
221-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:25:13-47
222            <meta-data
222-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:51:13-53:71
223                android:name="android.support.FILE_PROVIDER_PATHS"
223-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:52:17-67
224                android:resource="@xml/library_file_paths" />
224-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:53:17-68
225        </provider>
226        <provider
226-->[:expo-file-system] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
227            android:name="expo.modules.filesystem.FileSystemFileProvider"
227-->[:expo-file-system] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
228            android:authorities="com.synaptix.app.FileSystemFileProvider"
228-->[:expo-file-system] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
229            android:exported="false"
229-->[:expo-file-system] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
230            android:grantUriPermissions="true" >
230-->[:expo-file-system] C:\Users\<USER>\Projects\Synaptix\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
231            <meta-data
231-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:51:13-53:71
232                android:name="android.support.FILE_PROVIDER_PATHS"
232-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:52:17-67
233                android:resource="@xml/file_system_provider_paths" />
233-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:53:17-68
234        </provider>
235
236        <service
236-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:11:9-17:19
237            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
237-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:12:13-91
238            android:exported="false" >
238-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:13:13-37
239            <intent-filter android:priority="-1" >
239-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:13-16:29
239-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:28-49
240                <action android:name="com.google.firebase.MESSAGING_EVENT" />
240-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:17-78
240-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:25-75
241            </intent-filter>
242        </service>
243
244        <receiver
244-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:19:9-31:20
245            android:name="expo.modules.notifications.service.NotificationsService"
245-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:20:13-83
246            android:enabled="true"
246-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:21:13-35
247            android:exported="false" >
247-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:22:13-37
248            <intent-filter android:priority="-1" >
248-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:23:13-30:29
248-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:23:28-49
249                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
249-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:24:17-88
249-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:24:25-85
250                <action android:name="android.intent.action.BOOT_COMPLETED" />
250-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:25:17-79
250-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:25:25-76
251                <action android:name="android.intent.action.REBOOT" />
251-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:26:17-71
251-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:26:25-68
252                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
252-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:27:17-82
252-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:27:25-79
253                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
253-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:28:17-82
253-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:28:25-79
254                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
254-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:29:17-84
254-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:29:25-81
255            </intent-filter>
256        </receiver>
257
258        <activity
258-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:33:9-40:75
259            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
259-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:34:13-92
260            android:excludeFromRecents="true"
260-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:35:13-46
261            android:exported="false"
261-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:36:13-37
262            android:launchMode="standard"
262-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:37:13-42
263            android:noHistory="true"
263-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:38:13-37
264            android:taskAffinity=""
264-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:39:13-36
265            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
265-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:40:13-72
266
267        <receiver
267-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
268            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
268-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
269            android:exported="true"
269-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
270            android:permission="com.google.android.c2dm.permission.SEND" >
270-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
271            <intent-filter>
271-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
272                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
272-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
272-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
273            </intent-filter>
274
275            <meta-data
275-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
276                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
276-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
277                android:value="true" />
277-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
278        </receiver>
279        <!--
280             FirebaseMessagingService performs security checks at runtime,
281             but set to not exported to explicitly avoid allowing another app to call it.
282        -->
283        <service
283-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
284            android:name="com.google.firebase.messaging.FirebaseMessagingService"
284-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
285            android:directBootAware="true"
285-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
286            android:exported="false" >
286-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
287            <intent-filter android:priority="-500" >
287-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:13-16:29
287-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:28-49
288                <action android:name="com.google.firebase.MESSAGING_EVENT" />
288-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:17-78
288-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:25-75
289            </intent-filter>
290        </service>
291        <service
291-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
292            android:name="com.google.firebase.components.ComponentDiscoveryService"
292-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
293            android:directBootAware="true"
293-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
294            android:exported="false" >
294-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
295            <meta-data
295-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
296                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
296-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
297                android:value="com.google.firebase.components.ComponentRegistrar" />
297-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
298            <meta-data
298-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
299                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
299-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
300                android:value="com.google.firebase.components.ComponentRegistrar" />
300-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
301            <meta-data
301-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
302                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
302-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
303                android:value="com.google.firebase.components.ComponentRegistrar" />
303-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
304            <meta-data
304-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
305                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
305-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
306                android:value="com.google.firebase.components.ComponentRegistrar" />
306-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
307            <meta-data
307-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4afe4abd4836b02781314158b684bcde\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
308                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
308-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4afe4abd4836b02781314158b684bcde\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
309                android:value="com.google.firebase.components.ComponentRegistrar" />
309-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4afe4abd4836b02781314158b684bcde\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
310            <meta-data
310-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
311                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
311-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
312                android:value="com.google.firebase.components.ComponentRegistrar" />
312-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
313            <meta-data
313-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b69a5971adbf83c237b904f021022c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
314                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
314-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b69a5971adbf83c237b904f021022c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
315                android:value="com.google.firebase.components.ComponentRegistrar" />
315-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b69a5971adbf83c237b904f021022c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
316        </service>
317
318        <activity
318-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d29e10314c2a4b4c7355c13794550e06\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
319            android:name="com.google.android.gms.common.api.GoogleApiActivity"
319-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d29e10314c2a4b4c7355c13794550e06\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
320            android:exported="false"
320-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d29e10314c2a4b4c7355c13794550e06\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
321            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
321-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d29e10314c2a4b4c7355c13794550e06\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
322
323        <provider
323-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
324            android:name="com.google.firebase.provider.FirebaseInitProvider"
324-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
325            android:authorities="com.synaptix.app.firebaseinitprovider"
325-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
326            android:directBootAware="true"
326-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
327            android:exported="false"
327-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
328            android:initOrder="100" />
328-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
329        <provider
329-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
330            android:name="androidx.startup.InitializationProvider"
330-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
331            android:authorities="com.synaptix.app.androidx-startup"
331-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
332            android:exported="false" >
332-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
333            <meta-data
333-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
334                android:name="androidx.emoji2.text.EmojiCompatInitializer"
334-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
335                android:value="androidx.startup" />
335-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
336            <meta-data
336-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
337                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
337-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
338                android:value="androidx.startup" />
338-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
339            <meta-data
339-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
340                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
340-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
341                android:value="androidx.startup" />
341-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
342        </provider>
343
344        <meta-data
344-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef88a174ce7b920cee45b0a5e12a3fac\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
345            android:name="com.google.android.gms.version"
345-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef88a174ce7b920cee45b0a5e12a3fac\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
346            android:value="@integer/google_play_services_version" />
346-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef88a174ce7b920cee45b0a5e12a3fac\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
347
348        <receiver
348-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
349            android:name="androidx.profileinstaller.ProfileInstallReceiver"
349-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
350            android:directBootAware="false"
350-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
351            android:enabled="true"
351-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
352            android:exported="true"
352-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
353            android:permission="android.permission.DUMP" >
353-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
354            <intent-filter>
354-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
355                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
355-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
355-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
356            </intent-filter>
357            <intent-filter>
357-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
358                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
358-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
358-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
359            </intent-filter>
360            <intent-filter>
360-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
361                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
361-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
361-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
362            </intent-filter>
363            <intent-filter>
363-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
364                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
364-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
364-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
365            </intent-filter>
366        </receiver>
367
368        <service
368-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
369            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
369-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
370            android:exported="false" >
370-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
371            <meta-data
371-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
372                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
372-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
373                android:value="cct" />
373-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
374        </service>
375        <service
375-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
376            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
376-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
377            android:exported="false"
377-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
378            android:permission="android.permission.BIND_JOB_SERVICE" >
378-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
379        </service>
380
381        <receiver
381-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
382            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
382-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
383            android:exported="false" />
383-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
384    </application>
385
386</manifest>

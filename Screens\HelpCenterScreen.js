import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import CustomHeader from '../components/CustomHeader';
import { ensureStatusBarAppearance } from '../utils/screenTransition';
import { useSettings } from '../contexts/SettingsContext';

export default function HelpCenterScreen() {
  const navigation = useNavigation();
  const { darkModeEnabled } = useSettings();
  const [question, setQuestion] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Ensure StatusBar is properly configured when the screen mounts
  useEffect(() => {
    ensureStatusBarAppearance(darkModeEnabled);
  }, [darkModeEnabled]);

  const handleBackPress = () => {
    navigation.goBack();
  };

  const handleSubmitQuestion = () => {
    if (!question.trim()) {
      Alert.alert('Empty Question', 'Please enter your question before submitting.');
      return;
    }

    setIsSubmitting(true);

    // Simulate API call with timeout
    setTimeout(() => {
      setIsSubmitting(false);
      setQuestion('');
      Alert.alert(
        'Question Submitted',
        'Thank you for your question! Our support team will get back to you soon.',
        [{ text: 'OK' }]
      );
    }, 1000);
  };

  return (
    <View style={[
      styles.container,
      { backgroundColor: darkModeEnabled ? '#121212' : '#f5f5f5' }
    ]}>
      <StatusBar barStyle={darkModeEnabled ? "light-content" : "dark-content"} />
      <CustomHeader
        title="Help Center"
        showProfileIcon={false}
        showBackButton={true}
        onBackPress={handleBackPress} />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <ScrollView style={styles.scrollContainer}>
          <View style={styles.helpContainer}>
            <View style={[
              styles.helpCard,
              { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
            ]}>
              <View style={[
                styles.sectionHeader,
                { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
              ]}>
                <Text style={[
                  styles.sectionTitle,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>Frequently Asked Questions</Text>
              </View>

            <View style={styles.helpItem}>
              <View style={[
                styles.iconContainer,
                { backgroundColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
              ]}>
                <Ionicons
                  name="help-circle-outline"
                  size={24}
                  color={darkModeEnabled ? "#ffffff" : "#121212"}
                />
              </View>
              <View style={styles.helpContent}>
                <Text style={[
                  styles.helpTitle,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>How do I use the Connect screen?</Text>
                <Text style={[
                  styles.helpDescription,
                  { color: darkModeEnabled ? '#cccccc' : '#666666' }
                ]}>
                  Go to the Connect tab and tap the "Proceed to Results" button to see simulated brain analysis data. The app now uses simulated data instead of connecting to physical devices.
                </Text>
              </View>
            </View>

            <View style={styles.helpItem}>
              <View style={[
                styles.iconContainer,
                { backgroundColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
              ]}>
                <Ionicons
                  name="sync-outline"
                  size={24}
                  color={darkModeEnabled ? "#ffffff" : "#121212"}
                />
              </View>
              <View style={styles.helpContent}>
                <Text style={[
                  styles.helpTitle,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>How often should I sync my data?</Text>
                <Text style={[
                  styles.helpDescription,
                  { color: darkModeEnabled ? '#cccccc' : '#666666' }
                ]}>
                  We recommend syncing your data at least once a day for the most accurate analysis and insights.
                </Text>
              </View>
            </View>

            <View style={styles.helpItem}>
              <View style={[
                styles.iconContainer,
                { backgroundColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
              ]}>
                <Ionicons
                  name="battery-charging-outline"
                  size={24}
                  color={darkModeEnabled ? "#ffffff" : "#121212"}
                />
              </View>
              <View style={styles.helpContent}>
                <Text style={[
                  styles.helpTitle,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>How long does the battery last?</Text>
                <Text style={[
                  styles.helpDescription,
                  { color: darkModeEnabled ? '#cccccc' : '#666666' }
                ]}>
                  The device battery typically lasts 8-10 hours of continuous use. We recommend charging it overnight.
                </Text>
              </View>
            </View>
          </View>

          <View style={[
            styles.helpCard,
            { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
          ]}>
            <View style={[
              styles.sectionHeader,
              { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
            ]}>
              <Text style={[
                styles.sectionTitle,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>Contact Support</Text>
            </View>

            <TouchableOpacity style={styles.contactItem}>
              <View style={[
                styles.iconContainer,
                { backgroundColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
              ]}>
                <Ionicons
                  name="mail-outline"
                  size={24}
                  color={darkModeEnabled ? "#ffffff" : "#121212"}
                />
              </View>
              <View style={styles.helpContent}>
                <Text style={[
                  styles.helpTitle,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>Email Support</Text>
                <Text style={[
                  styles.helpDescription,
                  { color: darkModeEnabled ? '#cccccc' : '#666666' }
                ]}>
                  <EMAIL>
                </Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity style={styles.contactItem}>
              <View style={[
                styles.iconContainer,
                { backgroundColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
              ]}>
                <Ionicons
                  name="call-outline"
                  size={24}
                  color={darkModeEnabled ? "#ffffff" : "#121212"}
                />
              </View>
              <View style={styles.helpContent}>
                <Text style={[
                  styles.helpTitle,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>Phone Support</Text>
                <Text style={[
                  styles.helpDescription,
                  { color: darkModeEnabled ? '#cccccc' : '#666666' }
                ]}>
                  +****************
                </Text>
              </View>
            </TouchableOpacity>
          </View>

            {/* Post Your Question Box */}
            <View style={[
              styles.helpCard,
              { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
            ]}>
              <View style={[
                styles.sectionHeader,
                { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
              ]}>
                <Text style={[
                  styles.sectionTitle,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>Post Your Question</Text>
              </View>

              <View style={styles.questionContainer}>
                <View style={[
                  styles.questionInputContainer,
                  {
                    backgroundColor: darkModeEnabled ? '#2a2a2a' : '#f5f5f5',
                    borderColor: darkModeEnabled ? '#444444' : '#e0e0e0'
                  }
                ]}>
                  <TextInput
                    style={[
                      styles.questionInput,
                      { color: darkModeEnabled ? '#ffffff' : '#121212' }
                    ]}
                    placeholder="Type your question here..."
                    placeholderTextColor={darkModeEnabled ? '#9e9e9e' : '#757575'}
                    multiline={true}
                    numberOfLines={4}
                    value={question}
                    onChangeText={setQuestion}
                  />
                </View>

                <TouchableOpacity
                  style={[
                    styles.submitButton,
                    { opacity: isSubmitting ? 0.7 : 1 }
                  ]}
                  onPress={handleSubmitQuestion}
                  disabled={isSubmitting}
                >
                  <Text style={styles.submitButtonText}>
                    {isSubmitting ? 'Submitting...' : 'Submit Question'}
                  </Text>
                </TouchableOpacity>

                <Text style={[
                  styles.noteText,
                  { color: darkModeEnabled ? '#9e9e9e' : '#757575' }
                ]}>
                  Our support team typically responds within 24 hours.
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
    padding: 16,
  },
  helpContainer: {
    paddingBottom: 20,
  },
  helpCard: {
    borderRadius: 10,
    marginBottom: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionHeader: {
    padding: 16,
    borderBottomWidth: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  helpItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  contactItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  helpContent: {
    flex: 1,
  },
  helpTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  helpDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  questionContainer: {
    padding: 16,
  },
  questionInputContainer: {
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 16,
  },
  questionInput: {
    padding: 12,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    backgroundColor: '#d442f5',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  submitButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  noteText: {
    fontSize: 12,
    textAlign: 'center',
  },
});

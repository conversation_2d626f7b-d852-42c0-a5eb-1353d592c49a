// Minimal polyfills for React Native with <PERSON><PERSON>
import { <PERSON><PERSON><PERSON> } from 'buffer';
import process from 'process';
import { EventEmitter } from 'events';
import { decode, encode } from 'base-64';
import 'react-native-url-polyfill/auto';
import 'react-native-get-random-values';

// Fix for "Cannot read properties of undefined (reading 'bind')" error
// Ensure Function.prototype exists and has a bind method
if (typeof Function !== 'undefined' && Function.prototype && typeof Function.prototype.bind === 'undefined') {
  Function.prototype.bind = function(oThis) {
    if (typeof this !== 'function') {
      throw new TypeError('Function.prototype.bind - what is trying to be bound is not callable');
    }

    var aArgs = Array.prototype.slice.call(arguments, 1),
        fToBind = this,
        fNOP = function() {},
        fBound = function() {
          return fToBind.apply(this instanceof fNOP
                 ? this
                 : oThis,
                 aArgs.concat(Array.prototype.slice.call(arguments)));
        };

    if (this.prototype) {
      fNOP.prototype = this.prototype;
    }
    fBound.prototype = new fNOP();

    return fBound;
  };
}

// Ensure Function exists globally
if (typeof global.Function === 'undefined' && typeof Function !== 'undefined') {
  global.Function = Function;
}

// Make sure global.URL exists
if (typeof global.URL !== 'function') {
  global.URL = require('whatwg-url').URL;
}

// Basic global variables
global.__dirname = '/';
global.__filename = '';

// Required for the supabase client
if (!global.btoa) {
  global.btoa = encode;
}

if (!global.atob) {
  global.atob = decode;
}

// Basic process polyfill
global.process = process;
global.process.browser = false;
if (!global.process.nextTick) {
  global.process.nextTick = (callback, ...args) => setTimeout(() => callback(...args), 0);
}

// Basic Buffer polyfill
global.Buffer = Buffer;

// Basic EventEmitter polyfill
global.EventEmitter = EventEmitter;

// Basic crypto polyfill
if (!global.crypto) {
  global.crypto = {
    getRandomValues(arr) {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    },
  };
}

// Basic timers
global.setImmediate = global.setImmediate || ((fn, ...args) => setTimeout(fn, 0, ...args));
global.clearImmediate = global.clearImmediate || (id => clearTimeout(id));

// We don't need a WebSocket polyfill for this app since we're using Supabase's REST API
// If WebSocket functionality is needed in the future, use a proper polyfill library

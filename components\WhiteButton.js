import React, { useState, useEffect } from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  Animated,
  View
} from 'react-native';

/**
 * A button component with white background for the connect screen
 */
const WhiteButton = ({
  onPress,
  text,
  disabled = false,
  style,
  textStyle
}) => {
  // Animation value for the press effect
  const [scaleAnim] = useState(new Animated.Value(1));

  // Handle press in animation
  const handlePressIn = () => {
    try {
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 150,
        useNativeDriver: true,
      }).start();
    } catch (error) {
      console.error('Animation error on press in:', error);
    }
  };

  // Handle press out animation
  const handlePressOut = () => {
    try {
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    } catch (error) {
      console.error('Animation error on press out:', error);
    }
  };

  // Reset animation if there was an error
  useEffect(() => {
    return () => {
      try {
        scaleAnim.setValue(1);
      } catch (error) {
        console.error('Error resetting animation:', error);
      }
    };
  }, []);

  return (
    <Animated.View
      style={[
        styles.buttonContainer,
        { transform: [{ scale: scaleAnim }] },
        style
      ]}
    >
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => {
          try {
            if (onPress) onPress();
          } catch (error) {
            console.error('Button press error:', error);
          }
        }}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled}
        style={styles.touchable}
      >
        <View style={[
          styles.button,
          disabled ? styles.disabledButton : styles.activeButton
        ]}>
          <Text style={[
            styles.buttonText,
            disabled ? styles.disabledText : styles.activeText,
            textStyle
          ]}>
            {text}
          </Text>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  buttonContainer: {
    width: '100%',
    marginBottom: 20,
    borderRadius: 30,
    overflow: 'hidden',
  },
  touchable: {
    width: '100%',
  },
  button: {
    paddingVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 30,
  },
  activeButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#333333',
  },
  disabledButton: {
    backgroundColor: '#333333',
  },
  activeText: {
    color: '#333333',
  },
  disabledText: {
    color: '#999999',
  },
  buttonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default WhiteButton;

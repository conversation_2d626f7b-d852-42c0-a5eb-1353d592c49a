import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, ScrollView, StatusBar, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import GradientButton from '../components/GradientButton';
import EEGGraph from '../components/EEGGraph';
import { useSettings } from '../contexts/SettingsContext';
import { useBluetooth } from '../contexts/BluetoothContext';
import apiService from '../utils/apiService';

export default function ResultScreen({ navigation }) {
  const [loading, setLoading] = useState(true);
  const [results, setResults] = useState(null);
  const [analysisStatus, setAnalysisStatus] = useState('Initializing...');
  const [brainActivity, setBrainActivity] = useState('normal');
  const { darkModeEnabled } = useSettings();

  // Get Bluetooth context for real EEG data
  const {
    connectedDevice,
    eegData,
    deviceStatus
  } = useBluetooth();

  // Initialize the screen
  useEffect(() => {
    const initializeScreen = async () => {
      setAnalysisStatus('Setting up monitoring...');

      // Initial setup delay
      setTimeout(() => {
        setLoading(false);
        if (connectedDevice) {
          setAnalysisStatus('Monitoring brain activity...');
        } else {
          setAnalysisStatus('No device connected');
        }
      }, 2000);
    };

    initializeScreen();
  }, [connectedDevice]);

  // Real-time EEG data analysis with ML simulation
  useEffect(() => {
    if (eegData && eegData.channels && eegData.channels.length > 0) {
      setAnalysisStatus('🧠 Analyzing brain patterns...');

      // Advanced ML analysis simulation
      const analyzeData = async () => {
        try {
          // Get prediction results using real EEG data
          const predictionResults = await apiService.getPrediction(eegData);
          setResults(predictionResults);

          // Advanced brain activity analysis
          const channels = eegData.channels;
          const avgChannelValue = channels.reduce((sum, val) => sum + val, 0) / channels.length;
          const variance = channels.reduce((sum, val) => sum + Math.pow(val - avgChannelValue, 2), 0) / channels.length;
          const stdDev = Math.sqrt(variance);

          // Frequency domain analysis simulation
          const dominantFreq = simulateFrequencyAnalysis(channels);

          // Brain state classification
          let activityLevel, statusMessage, statusIcon;

          if (dominantFreq >= 13 && dominantFreq <= 30) {
            // Beta waves - active thinking
            activityLevel = 'high';
            statusIcon = '⚡';
            statusMessage = 'High cognitive activity (Beta waves)';
          } else if (dominantFreq >= 8 && dominantFreq <= 12) {
            // Alpha waves - relaxed awareness
            activityLevel = 'normal';
            statusIcon = '🧘';
            statusMessage = 'Relaxed awareness (Alpha waves)';
          } else if (dominantFreq >= 4 && dominantFreq <= 7) {
            // Theta waves - deep meditation/creativity
            activityLevel = 'meditative';
            statusIcon = '🌟';
            statusMessage = 'Deep meditation state (Theta waves)';
          } else if (dominantFreq >= 0.5 && dominantFreq <= 3) {
            // Delta waves - deep sleep
            activityLevel = 'low';
            statusIcon = '😴';
            statusMessage = 'Deep relaxation (Delta waves)';
          } else {
            // Mixed or unclear pattern
            activityLevel = 'mixed';
            statusIcon = '🔄';
            statusMessage = 'Mixed brain wave patterns';
          }

          // Additional analysis based on signal characteristics
          if (stdDev > 50) {
            statusMessage += ' - High signal variability';
          } else if (stdDev < 10) {
            statusMessage += ' - Stable signal';
          }

          setBrainActivity(activityLevel);
          setAnalysisStatus(`${statusIcon} ${statusMessage}`);

        } catch (error) {
          console.error('Analysis error:', error);
          setAnalysisStatus('⚠️ Analysis error occurred');
        }
      };

      // Debounce analysis to avoid too frequent updates
      const analysisTimeout = setTimeout(analyzeData, 1500);
      return () => clearTimeout(analysisTimeout);
    } else if (connectedDevice && deviceStatus === 'connected') {
      setAnalysisStatus('📡 Waiting for data...');
    } else if (connectedDevice && deviceStatus === 'streaming') {
      setAnalysisStatus('📊 Processing data stream...');
    }
  }, [eegData, connectedDevice, deviceStatus]);

  // Simulate frequency domain analysis
  const simulateFrequencyAnalysis = (channels) => {
    // Simple simulation of dominant frequency detection
    // In a real implementation, this would use FFT
    const avgValue = channels.reduce((sum, val) => sum + val, 0) / channels.length;
    const normalizedAvg = (avgValue / 255) * 40; // Scale to 0-40 Hz range

    // Add some randomness to simulate real frequency analysis
    const randomOffset = (Math.random() - 0.5) * 5;
    return Math.max(0.5, Math.min(40, normalizedAvg + randomOffset));
  };

  const renderScoreBar = (score, color) => {
    return (
      <View style={styles.scoreBarContainer}>
        <View style={[styles.scoreBar, { width: `${score}%`, backgroundColor: color }]} />
      </View>
    );
  };

  if (loading) {
    return (
      <View style={[
        styles.loadingContainer,
        { backgroundColor: darkModeEnabled ? '#121212' : '#f5f5f5' }
      ]}>
        <StatusBar barStyle={darkModeEnabled ? "light-content" : "dark-content"} />
        <ActivityIndicator size="large" color="#9C27B0" />
        <Text style={[
          styles.loadingText,
          { color: darkModeEnabled ? '#ffffff' : '#333333' }
        ]}>
          Setting up EEG monitoring...
        </Text>
      </View>
    );
  }

  return (
    <ScrollView style={[
      styles.container,
      { backgroundColor: darkModeEnabled ? '#121212' : '#f5f5f5' }
    ]}>
      <StatusBar barStyle={darkModeEnabled ? "light-content" : "dark-content"} />

      {/* Header */}
      <View style={styles.header}>
        <Text style={[
          styles.title,
          { color: darkModeEnabled ? '#ffffff' : '#333333' }
        ]}>
          EEG Live Monitor
        </Text>

        {/* Connection Status Indicator */}
        {connectedDevice && (
          <View style={[
            styles.connectionStatus,
            { backgroundColor: darkModeEnabled ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)' }
          ]}>
            <View style={[
              styles.statusDot,
              { backgroundColor: deviceStatus === 'streaming' ? '#4CAF50' : '#FFC107' }
            ]} />
            <Text style={[
              styles.connectionText,
              { color: darkModeEnabled ? '#ffffff' : '#333333' }
            ]}>
              {deviceStatus === 'streaming'
                ? `Live data from ${connectedDevice.name || 'device'}`
                : `Connected to ${connectedDevice.name || 'device'}`
              }
            </Text>
          </View>
        )}
      </View>

      {/* Real-time EEG Graph */}
      <EEGGraph
        eegData={eegData}
        isConnected={!!connectedDevice}
        deviceName={connectedDevice?.name || 'Unknown Device'}
      />

      {/* Analysis Status */}
      <View style={styles.analysisContainer}>
        <View style={[
          styles.analysisCard,
          {
            backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff',
            borderColor: darkModeEnabled ? '#333333' : '#e0e0e0'
          }
        ]}>
          <View style={styles.analysisHeader}>
            <Ionicons
              name="analytics-outline"
              size={24}
              color={darkModeEnabled ? '#9C27B0' : '#9C27B0'}
            />
            <Text style={[
              styles.analysisTitle,
              { color: darkModeEnabled ? '#ffffff' : '#333333' }
            ]}>
              Real-time Analysis
            </Text>
          </View>

          <Text style={[
            styles.analysisStatus,
            {
              color: brainActivity === 'high' ? '#FF5722' :
                     brainActivity === 'low' ? '#2196F3' : '#4CAF50'
            }
          ]}>
            {analysisStatus}
          </Text>

          {eegData && eegData.channels && (
            <View style={styles.dataStats}>
              <View style={styles.statItem}>
                <Text style={[
                  styles.statLabel,
                  { color: darkModeEnabled ? '#9e9e9e' : '#757575' }
                ]}>
                  Channels
                </Text>
                <Text style={[
                  styles.statValue,
                  { color: darkModeEnabled ? '#e0e0e0' : '#666666' }
                ]}>
                  {eegData.channels.length}
                </Text>
              </View>

              <View style={styles.statItem}>
                <Text style={[
                  styles.statLabel,
                  { color: darkModeEnabled ? '#9e9e9e' : '#757575' }
                ]}>
                  Signal Avg
                </Text>
                <Text style={[
                  styles.statValue,
                  { color: darkModeEnabled ? '#e0e0e0' : '#666666' }
                ]}>
                  {(eegData.channels.reduce((sum, val) => sum + val, 0) / eegData.channels.length).toFixed(1)} μV
                </Text>
              </View>

              <View style={styles.statItem}>
                <Text style={[
                  styles.statLabel,
                  { color: darkModeEnabled ? '#9e9e9e' : '#757575' }
                ]}>
                  Frequency
                </Text>
                <Text style={[
                  styles.statValue,
                  { color: darkModeEnabled ? '#e0e0e0' : '#666666' }
                ]}>
                  ~{simulateFrequencyAnalysis(eegData.channels).toFixed(1)} Hz
                </Text>
              </View>
            </View>
          )}
        </View>
      </View>

      {/* Results Summary (if available) */}
      {results && (
        <View style={styles.resultsContainer}>
          <View style={[
            styles.resultsCard,
            {
              backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff',
              borderColor: darkModeEnabled ? '#333333' : '#e0e0e0'
            }
          ]}>
            <Text style={[
              styles.sectionTitle,
              { color: darkModeEnabled ? '#ffffff' : '#333333' }
            ]}>
              Analysis Summary
            </Text>

            <View style={styles.scoreContainer}>
              <View style={[
                styles.overallScoreCircle,
                {
                  backgroundColor: darkModeEnabled ? '#2d2d2d' : '#f5f5f5',
                  borderColor: darkModeEnabled ? '#9C27B0' : '#9C27B0'
                }
              ]}>
                <Text style={[
                  styles.overallScoreText,
                  { color: darkModeEnabled ? '#ffffff' : '#333333' }
                ]}>
                  {results.overallScore}
                </Text>
              </View>
              <Text style={[
                styles.overallScoreLabel,
                { color: darkModeEnabled ? '#e0e0e0' : '#666666' }
              ]}>
                Overall Brain Performance
              </Text>
            </View>
          </View>
        </View>
      )}

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        {connectedDevice ? (
          <View style={styles.buttonRow}>
            <GradientButton
              text="DISCONNECT"
              onPress={() => {
                // Navigate back to connect screen
                navigation.navigate('ConnectTab');
              }}
              style={[styles.actionButton, { flex: 1, marginRight: 10 }]}
            />
            <GradientButton
              text="SAVE SESSION"
              onPress={() => {
                // TODO: Implement save session functionality
                console.log('Save session pressed');
              }}
              style={[styles.actionButton, { flex: 1, marginLeft: 10 }]}
            />
          </View>
        ) : (
          <GradientButton
            text="CONNECT DEVICE"
            onPress={() => {
              navigation.navigate('ConnectTab');
            }}
            style={styles.backButton}
          />
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 20,
    fontSize: 18,
    fontWeight: '500',
  },
  header: {
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  connectionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  connectionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  analysisContainer: {
    paddingHorizontal: 20,
    marginVertical: 10,
  },
  analysisCard: {
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
    shadowColor: '#000000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 3,
  },
  analysisHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  analysisTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  analysisStatus: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    marginVertical: 10,
  },
  dataStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 15,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  resultsContainer: {
    paddingHorizontal: 20,
    marginVertical: 10,
  },
  resultsCard: {
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
    shadowColor: '#000000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  scoreContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  overallScoreCircle: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 3,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  overallScoreText: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  overallScoreLabel: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  buttonContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: 40,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    marginVertical: 5,
  },
  backButton: {
    marginVertical: 10,
  },
});

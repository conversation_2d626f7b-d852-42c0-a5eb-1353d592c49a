import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  ScrollView,
  Alert,
  Switch,
} from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { useSettings } from '../contexts/SettingsContext';

export default function ProfileScreen({ navigation }) {
  const { currentUser, logout } = useAuth();
  const [activeTab, setActiveTab] = useState('account'); // 'account' or 'settings'

  // Get settings from context
  const {
    // Settings states
    notificationsEnabled,
    darkModeEnabled,
    dataBackupEnabled,

    // Loading states
    notificationsLoading,
    darkModeLoading,
    dataBackupLoading,

    // Toggle functions
    toggleNotifications,
    toggleDarkMode,
    toggleDataBackup
  } = useSettings();

  const handleLogout = async () => {
    const result = await logout();
    if (!result.success) {
      Alert.alert('Error', result.error);
    }
    // No need to navigate - the AppNavigator will automatically switch to AuthNavigator
    // when currentUser becomes null
  };

  // Format date if it exists
  const formatDate = (dateString) => {
    if (!dateString) return 'Not provided';

    // Check if the date is in MM/DD/YYYY format
    const dateRegex = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
    const match = dateString.match(dateRegex);

    if (match) {
      const month = parseInt(match[1], 10);
      const day = parseInt(match[2], 10);
      const year = parseInt(match[3], 10);

      // Create a more readable format
      const months = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];

      return `${months[month - 1]} ${day}, ${year}`;
    }

    return dateString;
  };

  const renderAccountTab = () => (
    <View style={styles.tabContent}>
      <View style={[
        styles.avatarPlaceholder,
        { backgroundColor: darkModeEnabled ? '#cccccc' : '#666666' }
      ]}>
        <Text style={[
          styles.avatarText,
          { color: darkModeEnabled ? '#333333' : '#ffffff' }
        ]}>
          {currentUser?.name ? currentUser.name.charAt(0).toUpperCase() : '?'}
        </Text>
      </View>

      <Text style={[
        styles.userName,
        { color: darkModeEnabled ? '#ffffff' : '#121212' }
      ]}>{currentUser?.name || 'User'}</Text>

      <View style={[
        styles.infoCard,
        { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
      ]}>
        <View style={[
          styles.sectionHeader,
          { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
        ]}>
          <Text style={[
            styles.sectionTitle,
            { color: darkModeEnabled ? '#ffffff' : '#121212' }
          ]}>Personal Information</Text>
        </View>

        <View style={[
          styles.infoRow,
          { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
        ]}>
          <Text style={[
            styles.infoLabel,
            { color: darkModeEnabled ? '#9e9e9e' : '#666666' }
          ]}>Email</Text>
          <Text style={[
            styles.infoValue,
            { color: darkModeEnabled ? '#ffffff' : '#121212' }
          ]}>{currentUser?.email || 'Not provided'}</Text>
        </View>

        <View style={[
          styles.infoRow,
          { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
        ]}>
          <Text style={[
            styles.infoLabel,
            { color: darkModeEnabled ? '#9e9e9e' : '#666666' }
          ]}>Date of Birth</Text>
          <Text style={[
            styles.infoValue,
            { color: darkModeEnabled ? '#ffffff' : '#121212' }
          ]}>{formatDate(currentUser?.dob)}</Text>
        </View>

        <View style={[
          styles.infoRow,
          { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
        ]}>
          <Text style={[
            styles.infoLabel,
            { color: darkModeEnabled ? '#9e9e9e' : '#666666' }
          ]}>Gender</Text>
          <Text style={[
            styles.infoValue,
            { color: darkModeEnabled ? '#ffffff' : '#121212' }
          ]}>
            {currentUser?.gender
              ? currentUser.gender.charAt(0).toUpperCase() + currentUser.gender.slice(1)
              : 'Not provided'}
          </Text>
        </View>

        {currentUser?.phone && (
          <View style={[
            styles.infoRow,
            { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
          ]}>
            <Text style={[
              styles.infoLabel,
              { color: darkModeEnabled ? '#9e9e9e' : '#666666' }
            ]}>Phone</Text>
            <Text style={[
              styles.infoValue,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>{currentUser.phone}</Text>
          </View>
        )}

        {currentUser?.address && (
          <View style={[
            styles.infoRow,
            { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
          ]}>
            <Text style={[
              styles.infoLabel,
              { color: darkModeEnabled ? '#9e9e9e' : '#666666' }
            ]}>Address</Text>
            <Text style={[
              styles.infoValue,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>{currentUser.address}</Text>
          </View>
        )}
      </View>

      <TouchableOpacity
        style={[
          styles.editButton,
          { backgroundColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
        ]}
        onPress={() => navigation.navigate('EditProfile')}
      >
        <Text style={[
          styles.editButtonText,
          { color: darkModeEnabled ? '#ffffff' : '#121212' }
        ]}>EDIT PROFILE</Text>
      </TouchableOpacity>
    </View>
  );

  const renderSettingsTab = () => (
    <View style={styles.tabContent}>
      <View style={[
        styles.settingsCard,
        { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
      ]}>
        <View style={[
          styles.sectionHeader,
          { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
        ]}>
          <Text style={[
            styles.sectionTitle,
            { color: darkModeEnabled ? '#ffffff' : '#121212' }
          ]}>App Settings</Text>
        </View>

        <View style={[
          styles.settingRow,
          { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
        ]}>
          <View style={styles.settingInfo}>
            <Text style={[
              styles.settingLabel,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>Notifications</Text>
            <Text style={[
              styles.settingDescription,
              { color: darkModeEnabled ? '#9e9e9e' : '#666666' }
            ]}>Receive alerts and updates</Text>
          </View>
          <Switch
            value={notificationsEnabled}
            onValueChange={async (value) => {
              if (notificationsLoading) return;
              await toggleNotifications(value);
            }}
            trackColor={{ false: darkModeEnabled ? '#333' : '#cccccc', true: darkModeEnabled ? '#ffffff' : '#d0d0d0' }}
            thumbColor={notificationsEnabled ? (darkModeEnabled ? '#9e9e9e' : '#8a8a8a') : (darkModeEnabled ? '#666666' : '#f4f3f4')}
            disabled={notificationsLoading}
          />
        </View>

        <View style={[
          styles.settingRow,
          { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
        ]}>
          <View style={styles.settingInfo}>
            <Text style={[
              styles.settingLabel,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>Dark Mode</Text>
            <Text style={[
              styles.settingDescription,
              { color: darkModeEnabled ? '#9e9e9e' : '#666666' }
            ]}>Use dark theme</Text>
          </View>
          <Switch
            value={darkModeEnabled}
            onValueChange={async (value) => {
              if (darkModeLoading) return;
              await toggleDarkMode(value);
            }}
            trackColor={{ false: darkModeEnabled ? '#333' : '#cccccc', true: darkModeEnabled ? '#ffffff' : '#d0d0d0' }}
            thumbColor={darkModeEnabled ? (darkModeEnabled ? '#9e9e9e' : '#8a8a8a') : (darkModeEnabled ? '#666666' : '#f4f3f4')}
            disabled={darkModeLoading}
          />
        </View>

        <View style={[
          styles.settingRow,
          { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
        ]}>
          <View style={styles.settingInfo}>
            <Text style={[
              styles.settingLabel,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>Data Backup</Text>
            <Text style={[
              styles.settingDescription,
              { color: darkModeEnabled ? '#9e9e9e' : '#666666' }
            ]}>Automatically backup your data</Text>
          </View>
          <Switch
            value={dataBackupEnabled}
            onValueChange={async (value) => {
              if (dataBackupLoading) return;
              await toggleDataBackup(value);
            }}
            trackColor={{ false: darkModeEnabled ? '#333' : '#cccccc', true: darkModeEnabled ? '#ffffff' : '#d0d0d0' }}
            thumbColor={dataBackupEnabled ? (darkModeEnabled ? '#9e9e9e' : '#8a8a8a') : (darkModeEnabled ? '#666666' : '#f4f3f4')}
            disabled={dataBackupLoading}
          />
        </View>
      </View>

      <View style={[
        styles.settingsCard,
        { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
      ]}>
        <View style={[
          styles.sectionHeader,
          { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
        ]}>
          <Text style={[
            styles.sectionTitle,
            { color: darkModeEnabled ? '#ffffff' : '#121212' }
          ]}>About</Text>
        </View>

        <TouchableOpacity
          style={[
            styles.aboutRow,
            { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
          ]}
          onPress={() => navigation.navigate('PrivacyPolicy')}
        >
          <Text style={[
            styles.aboutLabel,
            { color: darkModeEnabled ? '#ffffff' : '#121212' }
          ]}>Privacy Policy</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.aboutRow,
            { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
          ]}
          onPress={() => navigation.navigate('TermsOfService')}
        >
          <Text style={[
            styles.aboutLabel,
            { color: darkModeEnabled ? '#ffffff' : '#121212' }
          ]}>Terms of Service</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[
          styles.aboutRow,
          { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
        ]}>
          <Text style={[
            styles.aboutLabel,
            { color: darkModeEnabled ? '#ffffff' : '#121212' }
          ]}>App Version</Text>
          <Text style={[
            styles.aboutValue,
            { color: darkModeEnabled ? '#9e9e9e' : '#666666' }
          ]}>1.0.0</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={[
      styles.container,
      { backgroundColor: darkModeEnabled ? '#121212' : '#f5f5f5' }
    ]}>
      <StatusBar barStyle={darkModeEnabled ? "light-content" : "dark-content"} />
      <View style={[
        styles.header,
        { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
      ]}>
        <Text style={[
          styles.title,
          { color: darkModeEnabled ? '#ffffff' : '#121212' }
        ]}>My Profile</Text>
      </View>

      <View style={[
        styles.tabBar,
        { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
      ]}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'account' && {
              borderBottomColor: darkModeEnabled ? '#ffffff' : '#121212'
            }
          ]}
          onPress={() => setActiveTab('account')}
        >
          <Text style={[
            styles.tabButtonText,
            { color: darkModeEnabled ? '#9e9e9e' : '#666666' },
            activeTab === 'account' && {
              color: darkModeEnabled ? '#ffffff' : '#121212',
              fontWeight: 'bold'
            }
          ]}>
            Account
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'settings' && {
              borderBottomColor: darkModeEnabled ? '#ffffff' : '#121212'
            }
          ]}
          onPress={() => setActiveTab('settings')}
        >
          <Text style={[
            styles.tabButtonText,
            { color: darkModeEnabled ? '#9e9e9e' : '#666666' },
            activeTab === 'settings' && {
              color: darkModeEnabled ? '#ffffff' : '#121212',
              fontWeight: 'bold'
            }
          ]}>
            Settings
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollContainer}>
        <View style={styles.profileContainer}>
          {activeTab === 'account' ? renderAccountTab() : renderSettingsTab()}
        </View>

        <TouchableOpacity
          style={styles.logoutButton}
          onPress={handleLogout}
        >
          <Text style={styles.logoutButtonText}>LOGOUT</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  tabBar: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 15,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  activeTabButtonText: {
    fontWeight: 'bold',
  },
  scrollContainer: {
    flex: 1,
  },
  profileContainer: {
    padding: 20,
  },
  tabContent: {
    alignItems: 'center',
  },
  avatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  avatarText: {
    fontSize: 40,
    fontWeight: 'bold',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
  },
  infoCard: {
    borderRadius: 10,
    padding: 20,
    width: '100%',
    marginBottom: 30,
  },
  settingsCard: {
    borderRadius: 10,
    padding: 20,
    width: '100%',
    marginBottom: 20,
  },
  sectionHeader: {
    borderBottomWidth: 1,
    paddingBottom: 15,
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 15,
    borderBottomWidth: 1,
  },
  infoLabel: {
    fontSize: 16,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '500',
    maxWidth: '60%',
    textAlign: 'right',
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
  },
  settingInfo: {
    flex: 1,
    marginRight: 10,
  },
  settingLabel: {
    fontSize: 16,
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
  },
  aboutRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
  },
  aboutLabel: {
    fontSize: 16,
  },
  aboutValue: {
    fontSize: 16,
  },
  editButton: {
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 30,
    marginBottom: 20,
  },
  editButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  logoutButton: {
    backgroundColor: '#ff5252',
    paddingVertical: 15,
    borderRadius: 30,
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 30,
  },
  logoutButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

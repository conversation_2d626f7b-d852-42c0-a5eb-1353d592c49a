{"logs": [{"outputFile": "com.synaptix.app-mergeDebugResources-55:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6550f91160cccf4761958ba318c6d009\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,42,43,44,45,46,47,48,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "977,1052,1163,1252,1353,1460,1567,1666,1773,1876,2003,2091,2215,2317,2419,2535,2637,2751,2879,2995,3117,3253,3373,3507,3627,3739,3954,4071,4195,4325,4447,4585,4719,4835", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "1047,1158,1247,1348,1455,1562,1661,1768,1871,1998,2086,2210,2312,2414,2530,2632,2746,2874,2990,3112,3248,3368,3502,3622,3734,3860,4066,4190,4320,4442,4580,4714,4830,4950"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f64dc84a821f94336e410d15a520d5cf\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "8,9,10,11,12,13,14,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "345,415,499,583,679,781,883,3865", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "410,494,578,674,776,878,972,3949"}}, {"source": "C:\\Users\\<USER>\\Projects\\Synaptix\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,5", "startColumns": "4,4", "startOffsets": "55,200", "endLines": "4,7", "endColumns": "9,9", "endOffsets": "195,340"}}]}]}
{"plugins": [], "android": {"package": "com.synaptix.app", "permissions": [], "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#121212"}}, "name": "Synaptix", "slug": "Synaptix", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": false, "splash": {"image": "./assets/logo_splashscreen-removebg-preview.png", "resizeMode": "contain", "backgroundColor": "#121212"}, "jsEngine": "jsc", "scheme": "synaptix", "ios": {"supportsTablet": true, "bundleIdentifier": "com.synaptix.app", "infoPlist": {}}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "bce97cf0-0199-4ed8-940f-c58f931c8a9e"}}, "sdkVersion": "53.0.0", "platforms": ["ios", "android", "web"]}
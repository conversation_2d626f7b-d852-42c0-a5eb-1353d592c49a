import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';
import Svg, { Path, Line, G, Rect } from 'react-native-svg';
import { useSettings } from '../contexts/SettingsContext';

/**
 * Simple EEG Animation Component
 *
 * Displays a continuous EEG waveform running in a loop
 * The animation mimics a brain waveform signal in purple color
 */
const SimpleEEGAnimation = ({ style }) => {
  const { darkModeEnabled } = useSettings();

  // Animation value for the EEG waveform
  const eegAnim = useRef(new Animated.Value(0)).current;

  // Start the animation when the component mounts
  useEffect(() => {
    // Create a truly continuous animation
    const createContinuousAnimation = () => {
      // Reset the animation value to 0
      eegAnim.setValue(0);

      // Create the animation with linear easing for smooth movement
      Animated.timing(eegAnim, {
        toValue: 1,
        duration: 15000, // 15 seconds for a complete cycle
        useNativeDriver: true,
        easing: Easing.linear, // Linear easing for smooth movement
      }).start((result) => {
        // When animation completes, restart immediately without any delay
        if (result.finished) {
          // Reset and restart immediately
          requestAnimationFrame(createContinuousAnimation);
        }
      });
    };

    // Start the continuous animation
    createContinuousAnimation();

    // Clean up animations when component unmounts
    return () => {
      eegAnim.stopAnimation();
    };
  }, []);

  // Make the background transparent to show the grid
  const backgroundColor = 'transparent'; // Fully transparent background

  // EEG line color - exact purple from the image
  const eegLineColor = '#C932FF'; // Bright purple from the image

  // Grid colors - using light white for a subtle effect
  const gridLineColor = 'rgba(255, 255, 255, 0.15)'; // Light white for grid lines
  const gridMajorLineColor = 'rgba(255, 255, 255, 0.25)'; // Slightly brighter for major grid lines
  const gridBackgroundColor = 'rgba(0, 0, 0, 0.85)'; // Nearly black background for the grid

  // Horizontal translation for the EEG waveform
  const translateX = eegAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -1600], // Move the entire SVG to create continuous loop
    extrapolate: 'clamp',
  });

  // Create the EEG grid component
  const renderGrid = () => {
    // Create horizontal and vertical grid lines
    const horizontalLines = [];
    const verticalLines = [];

    // Number of grid lines (more lines = finer grid)
    const horizontalCount = 20; // Increased for finer grid
    const verticalCount = 40; // Increased for finer grid

    // Create horizontal grid lines
    for (let i = 0; i <= horizontalCount; i++) {
      const y = (i / horizontalCount) * 200;
      const isMajor = i % 5 === 0; // Every 5th line is a major line

      horizontalLines.push(
        <Line
          key={`h-${i}`}
          x1="0"
          y1={y}
          x2="100%"
          y2={y}
          stroke={isMajor ? gridMajorLineColor : gridLineColor}
          strokeWidth={isMajor ? 1 : 0.5}
        />
      );
    }

    // Create vertical grid lines
    for (let i = 0; i <= verticalCount; i++) {
      const x = (i / verticalCount) * 100 + '%';
      const isMajor = i % 5 === 0; // Every 5th line is a major line

      verticalLines.push(
        <Line
          key={`v-${i}`}
          x1={x}
          y1="0"
          x2={x}
          y2="100%"
          stroke={isMajor ? gridMajorLineColor : gridLineColor}
          strokeWidth={isMajor ? 1 : 0.5}
        />
      );
    }

    return (
      <Svg height="100%" width="100%" style={styles.gridSvg}>
        {/* Background rectangle */}
        <Rect
          x="0"
          y="0"
          width="100%"
          height="100%"
          fill={gridBackgroundColor}
        />
        {/* Grid lines */}
        <G>
          {horizontalLines}
          {verticalLines}
        </G>
      </Svg>
    );
  };

  return (
    <Animated.View style={[styles.container, style]}>
      {/* Grid background */}
      <View style={styles.gridContainer}>
        {renderGrid()}
      </View>

      {/* Container for the EEG waveform */}
      <View style={styles.eegContainer}>
        {/* Animated SVG container */}
        <Animated.View
          style={[
            styles.svgContainer,
            {
              transform: [{ translateX }]
            }
          ]}
        >
          {/* First EEG waveform */}
          <Svg height="100%" width={1600} style={styles.svg}>
            <Path
              d={`
                M 0,100
                L 20,90 40,110 60,90 80,110
                L 100,85 120,115 140,85 160,115
                L 180,80 200,120 220,80 240,120

                L 260,75 280,125 300,75 320,125
                L 340,90 360,110 380,90 400,110

                L 420,85 440,115 460,85 480,115
                L 500,80 520,120 540,80 560,120

                L 580,85 590,115 595,80 600,120 605,80 610,115 620,85 640,115
                L 660,90 680,110 700,90 720,110

                L 740,85 760,115 780,85 800,115
                L 820,75 840,125 860,75 880,125

                L 900,85 920,115 940,85 960,115
                L 980,90 1000,110 1020,90 1040,110

                L 1060,80 1080,120 1100,100 1110,100 1120,100 1130,100 1140,100 1150,100
                L 1160,90 1180,110 1200,90 1220,110

                L 1220,85 1240,115 1260,85 1280,115
                L 1300,75 1320,125 1340,75 1360,125

                L 1380,85 1385,115 1390,80 1395,120 1400,80 1405,115 1410,85 1415,115 1420,85 1425,115 1430,85 1435,115 1440,85
                L 1460,90 1480,110 1500,90 1520,110

                L 1540,85 1560,115 1580,85 1600,100
              `}
              fill="none"
              stroke={eegLineColor}
              strokeWidth="2.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </Svg>

          {/* Duplicate the waveform to create a seamless loop */}
          <Svg height="100%" width={1600} style={[styles.svg, { left: 1600 }]}>
            <Path
              d={`
                M 0,100
                L 20,90 40,110 60,90 80,110
                L 100,85 120,115 140,85 160,115
                L 180,80 200,120 220,80 240,120

                L 260,75 280,125 300,75 320,125
                L 340,90 360,110 380,90 400,110

                L 420,85 440,115 460,85 480,115
                L 500,80 520,120 540,80 560,120

                L 580,85 590,115 595,80 600,120 605,80 610,115 620,85 640,115
                L 660,90 680,110 700,90 720,110

                L 740,85 760,115 780,85 800,115
                L 820,75 840,125 860,75 880,125

                L 900,85 920,115 940,85 960,115
                L 980,90 1000,110 1020,90 1040,110

                L 1060,80 1080,120 1100,100 1110,100 1120,100 1130,100 1140,100 1150,100
                L 1160,90 1180,110 1200,90 1220,110

                L 1220,85 1240,115 1260,85 1280,115
                L 1300,75 1320,125 1340,75 1360,125

                L 1380,85 1385,115 1390,80 1395,120 1400,80 1405,115 1410,85 1415,115 1420,85 1425,115 1430,85 1435,115 1440,85
                L 1460,90 1480,110 1500,90 1520,110

                L 1540,85 1560,115 1580,85 1600,100
              `}
              fill="none"
              stroke={eegLineColor}
              strokeWidth="2.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </Svg>
        </Animated.View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: '100%',
    borderRadius: 15,
    overflow: 'hidden',
    position: 'relative',
    // Enhanced shadow and border effects
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.15)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    backgroundColor: 'transparent', // Make container transparent to show grid
  },
  // Grid container
  gridContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1, // Place grid behind the EEG waveform
  },
  // Grid SVG styling
  gridSvg: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  // Container for the EEG waveform
  eegContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    zIndex: 2, // Place EEG waveform above the grid
  },
  // SVG container that will be animated
  svgContainer: {
    position: 'absolute',
    height: '100%',
    width: 3200, // Double the SVG width to allow for seamless looping
    flexDirection: 'row',
    left: 0, // Ensure the container starts at the left edge
  },
  // SVG styling
  svg: {
    position: 'absolute',
    height: '100%',
    left: 0, // Ensure the first SVG starts at the left edge
  }
});

export default SimpleEEGAnimation;

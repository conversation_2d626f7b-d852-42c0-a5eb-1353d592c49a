import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Image,
  Alert,
  FlatList,
} from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { validateEmail, validatePassword } from '../utils/validation';
import GradientButton from '../components/GradientButton';



export default function LoginScreen({ navigation, route }) {
  const {
    login,
    currentUser,
    skipToHome,
  } = useAuth();

  const [email, setEmail] = useState(route?.params?.email || '');
  const [password, setPassword] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [authError, setAuthError] = useState(''); // New state for authentication errors
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSkipping, setIsSkipping] = useState(false);

  // Custom email setter with validation
  const handleEmailChange = (text) => {
    setEmail(text);

    // Clear error when user starts typing
    if (emailError) {
      setEmailError('');
    }

    // Real-time validation for email
    if (text && !validateEmail(text)) {
      setEmailError('Please enter a valid email (e.g., <EMAIL>)');
    }
  };



  // Note: Navigation is handled automatically by AppNavigator based on currentUser state
  // No manual navigation needed here



  // Handle login with provided credentials (used for auto-login)
  const handleLogin = async (emailToUse, passwordToUse) => {
    const emailToSubmit = emailToUse || email;
    const passwordToSubmit = passwordToUse || password;

    let isValid = true;

    // Reset errors
    setEmailError('');
    setPasswordError('');
    setAuthError(''); // Clear any previous auth errors

    // Validate email
    if (!emailToSubmit) {
      setEmailError('Email is required');
      isValid = false;
    } else if (!validateEmail(emailToSubmit)) {
      setEmailError('Please enter a valid email (e.g., <EMAIL>)');
      isValid = false;
    }

    // Validate password
    const passwordValidation = validatePassword(passwordToSubmit);
    if (!passwordValidation.isValid) {
      setPasswordError(passwordValidation.message);
      isValid = false;
    }

    if (isValid) {
      setIsLoading(true);
      try {
        // Call login function from auth context
        const result = await login(emailToSubmit, passwordToSubmit);

        if (result.success) {
          // Navigation is handled by the useEffect that watches currentUser
        } else {
          // Set the specific error message returned from the auth context
          setAuthError(result.error || 'Please check your credentials and try again.');

          // If the error indicates the email is not registered, offer to navigate to signup
          if (result.error && result.error.includes('Account has not been registered')) {
            Alert.alert(
              'Account Not Found',
              'Would you like to create a new account with this email?',
              [
                {
                  text: 'Cancel',
                  style: 'cancel',
                },
                {
                  text: 'Sign Up',
                  onPress: () => navigation.navigate('Signup', { email: emailToSubmit }),
                },
              ]
            );
          }
        }
      } catch (error) {
        setAuthError('An unexpected error occurred. Please try again.');
        console.error(error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Using validation functions from utils/validation.js

  // Handle login button press
  const handleLoginButtonPress = () => {
    handleLogin(null, null);
  };

  // Handle skip to home
  const handleSkip = async () => {
    setIsSkipping(true);
    try {
      const result = await skipToHome();
      if (result.success) {
        // Navigation is handled automatically by AppNavigator based on currentUser state
        // No manual navigation needed here
      } else {
        setAuthError('Failed to skip to home. Please try again.');
      }
    } catch (error) {
      setAuthError('An unexpected error occurred. Please try again.');
      console.error(error);
    } finally {
      setIsSkipping(false);
    }
  };





  // Reference to the ScrollView
  const scrollViewRef = useRef();

  // Function to scroll to the password field
  const scrollToPassword = () => {
    // Add a longer delay to ensure the keyboard is fully shown
    setTimeout(() => {
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollTo({ y: 150, animated: true });
      }
    }, 300);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 30}
      enabled
    >
      <StatusBar barStyle="light-content" />
      <ScrollView
        ref={scrollViewRef}
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="always"
        showsVerticalScrollIndicator={false}
        keyboardDismissMode="interactive">
        <View style={styles.logoContainer}>
          <Text style={styles.logoText}>Synaptix</Text>
          <Text style={styles.tagline}>Advanced Neural Analysis</Text>
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.label}>Email</Text>
          <TextInput
            style={[styles.input, emailError ? styles.inputError : null]}
            placeholder="Enter your email"
            placeholderTextColor="#666"
            keyboardType="email-address"
            autoCapitalize="none"
            value={email}
            onChangeText={handleEmailChange}
          />

          {emailError ? <Text style={styles.errorText}>{emailError}</Text> : null}

          <Text style={styles.label}>Password</Text>
          <View style={styles.passwordContainer}>
            <TextInput
              style={[styles.passwordInput, passwordError ? styles.inputError : null]}
              placeholder="Enter your password"
              placeholderTextColor="#666"
              secureTextEntry={!showPassword}
              value={password}
              onChangeText={setPassword}
              onFocus={scrollToPassword}
            />
            <TouchableOpacity
              style={styles.eyeIcon}
              onPress={() => setShowPassword(!showPassword)}
            >
              <Text style={styles.eyeIconText}>{showPassword ? '👁️' : '👁️‍🗨️'}</Text>
            </TouchableOpacity>
          </View>
          {passwordError ? <Text style={styles.errorText}>{passwordError}</Text> : null}

          {/* Authentication Error Message */}
          {authError ? (
            <View style={styles.authErrorContainer}>
              <Text style={styles.authErrorText}>{authError}</Text>
            </View>
          ) : null}

          <View style={styles.rememberForgotContainer}>
            <TouchableOpacity style={styles.forgotPassword}>
              <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
            </TouchableOpacity>
          </View>

          <GradientButton
            text={isLoading ? 'LOGGING IN...' : 'LOGIN'}
            onPress={handleLoginButtonPress}
            disabled={isLoading || isSkipping}
            style={styles.loginButton}
          />

          {/* Skip Button */}
          <TouchableOpacity
            style={styles.skipButton}
            onPress={handleSkip}
            disabled={isLoading || isSkipping}
          >
            <Text style={styles.skipButtonText}>
              {isSkipping ? 'SKIPPING...' : 'SKIP TO HOME'}
            </Text>
          </TouchableOpacity>

          <View style={styles.signupContainer}>
            <Text style={styles.signupText}>Don't have an account? </Text>
            <TouchableOpacity onPress={() => navigation.navigate('Signup')}>
              <Text style={styles.signupLink}>Sign Up</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
    paddingBottom: 120, // Increased padding at the bottom to ensure content is visible above keyboard
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 60,
    marginBottom: 40,
  },
  logoText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  tagline: {
    fontSize: 16,
    color: '#ffffff',
    marginTop: 5,
  },
  formContainer: {
    backgroundColor: '#1e1e1e',
    borderRadius: 10,
    padding: 20,
    marginBottom: 20,
  },
  label: {
    color: '#ffffff',
    fontSize: 16,
    marginBottom: 5,
    marginTop: 15,
  },
  input: {
    backgroundColor: '#333333',
    borderRadius: 5,
    padding: 15,
    color: '#ffffff',
    fontSize: 16,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#333333',
    borderRadius: 5,
  },
  passwordInput: {
    flex: 1,
    padding: 15,
    color: '#ffffff',
    fontSize: 16,
    backgroundColor: 'transparent',
  },
  eyeIcon: {
    padding: 15,
  },
  eyeIconText: {
    fontSize: 18,
    color: '#e0e0e0',
  },
  inputError: {
    borderWidth: 1,
    borderColor: '#ff5252',
  },
  errorText: {
    color: '#ff5252',
    fontSize: 14,
    marginTop: 5,
  },
  authErrorContainer: {
    backgroundColor: '#2a2a2a',
    borderRadius: 5,
    padding: 10,
    marginTop: 15,
    borderLeftWidth: 4,
    borderLeftColor: '#444444',
  },
  authErrorText: {
    color: '#e0e0e0',
    fontSize: 14,
  },
  rememberForgotContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  switchAccountButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  switchAccountText: {
    color: '#ffffff',
    fontSize: 14,
  },
  accountSelectorButton: {
    position: 'absolute',
    right: 10,
    top: 12,
    padding: 5,
  },
  savedAccountsContainer: {
    backgroundColor: '#333333',
    borderRadius: 5,
    marginTop: 5,
    maxHeight: 200,
  },
  savedAccountItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#444444',
  },
  savedAccountImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
  },
  savedAccountImagePlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  savedAccountImageText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  savedAccountInfo: {
    flex: 1,
  },
  savedAccountName: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  savedAccountEmail: {
    color: '#e0e0e0',
    fontSize: 12,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
  },
  forgotPasswordText: {
    color: '#ffffff',
    fontSize: 14,
  },
  loginButton: {
    marginBottom: 10,
  },
  skipButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#666',
    borderRadius: 25,
    paddingVertical: 15,
    paddingHorizontal: 30,
    alignItems: 'center',
    marginBottom: 20,
  },
  skipButtonText: {
    color: '#999',
    fontSize: 16,
    fontWeight: '600',
  },
  signupContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
  },
  signupText: {
    color: '#e0e0e0',
    fontSize: 16,
  },
  signupLink: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

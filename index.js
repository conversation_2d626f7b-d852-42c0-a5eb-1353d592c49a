// Import polyfills first - this must be the very first import
// This ensures all necessary JavaScript functionality is available
import './polyfills';

// Import debug file to check if polyfills are working
import './debug-polyfills';

// Only import other modules after polyfills are loaded
import { registerRootComponent } from 'expo';
import App from './App';

// registerRootComponent calls AppRegistry.registerComponent('main', () => App);
// It also ensures that whether you load the app in Expo Go or in a native build,
// the environment is set up appropriately
registerRootComponent(App);

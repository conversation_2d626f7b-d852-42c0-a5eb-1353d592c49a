# Synaptix App

## Authentication System with Supabase

This app implements a secure authentication system using Supabase as the backend service. It features account persistence similar to Instagram, allowing users to save their login information and switch between multiple accounts.

## Supabase Setup

The app uses Supabase for authentication and data storage. To set up your own Supabase project:

1. Create a new project at [supabase.com](https://supabase.com)
2. Set up the database schema:
   - The app uses a `profiles` table to store user information
   - Enable Row Level Security (RLS) policies to secure user data
3. Configure authentication settings:
   - Enable email/password authentication
   - Set up the site URL to match your app's URL scheme (e.g., `synaptix://auth`)
4. Create a `.env` file in the project root with your Supabase credentials:
   ```
   SUPABASE_URL=https://your-project-id.supabase.co
   SUPABASE_ANON_KEY=your-anon-key
   ```

## Testing the Authentication System

You can test the authentication system by following these steps:

1. Run the app
2. Sign up with a new account
3. When prompted, choose "Save Info" to save your account
4. Log out
5. You should see your saved account on the login screen
6. Try logging in with your saved account
7. Try adding another account by signing up again
8. Test switching between accounts

## Features

- Secure user registration and login with email/password via Supabase
- Account persistence across app reinstalls
- Account switching functionality
- Profile management with data stored in Supabase
- Secure storage of authentication tokens using SecureStore

## Implementation Details

- Authentication is handled by the AuthContext using Supabase Auth
- User profiles are stored in a Supabase database table
- Account persistence is implemented using AsyncStorage and SecureStore
- Row Level Security (RLS) policies ensure users can only access their own data
- The UI includes components for account selection and saving

## Troubleshooting

If you encounter any issues:

1. Check the console for error messages
2. Make sure you have the latest dependencies installed
3. Verify your Supabase configuration in the `.env` file
4. Check your Supabase project settings for authentication and database configuration
5. Try clearing the app data and reinstalling if necessary

## Security Features

The app implements several security best practices:

- Authentication tokens are stored securely using expo-secure-store
- User data is protected by Row Level Security policies in Supabase
- Passwords are never stored in the app, only on the Supabase backend
- Session management is handled by Supabase Auth
- API keys are stored in environment variables

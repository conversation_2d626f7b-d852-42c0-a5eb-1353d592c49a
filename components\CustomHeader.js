import React, { useEffect } from 'react';
import { View, Text, StyleSheet, StatusBar, Platform, SafeAreaView, Image, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import ProfileButton from './ProfileButton';
import { ensureStatusBarAppearance } from '../utils/screenTransition';
import { useSettings } from '../contexts/SettingsContext';

export default function CustomHeader({
  title,
  onProfilePress,
  showProfileIcon = true,
  showLogo = false,
  showBackButton = false,
  onBackPress = null
}) {
  const navigation = useNavigation();
  const { darkModeEnabled } = useSettings();

  // Ensure StatusBar is properly configured when the header mounts
  useEffect(() => {
    ensureStatusBarAppearance(darkModeEnabled);
  }, [darkModeEnabled]);

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      navigation.goBack();
    }
  };

  return (
    <SafeAreaView style={[
      styles.safeArea,
      { backgroundColor: darkModeEnabled ? '#121212' : '#f5f5f5' }
    ]}>
      <View style={[
        styles.headerContainer,
        { backgroundColor: darkModeEnabled ? '#121212' : '#f5f5f5' }
      ]}>
        {/* Header bar with logo and profile icon aligned */}
        <View style={styles.headerBar}>
          {/* Back button or Logo at the left */}
          {showBackButton ? (
            <TouchableOpacity
              style={styles.backButtonContainer}
              onPress={handleBackPress}
            >
              <Ionicons
                name="arrow-back"
                size={24}
                color={darkModeEnabled ? "#ffffff" : "#121212"}
              />
            </TouchableOpacity>
          ) : showLogo && (
            <View style={styles.logoContainer}>
              <Image
                source={require('../assets/synaptix_logo_-removebg-preview.png')}
                style={styles.logo}
                resizeMode="contain"
              />
            </View>
          )}

          {/* Title in the middle */}
          {title ? (
            <Text style={[
              styles.title,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>{title}</Text>
          ) : (
            <View style={styles.spacer} />
          )}

          {/* Profile icon at the right */}
          {showProfileIcon ? (
            <View style={styles.profileIconContainer}>
              <ProfileButton onPress={onProfilePress} />
            </View>
          ) : (
            <View style={styles.placeholderRight} />
          )}
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    paddingTop: 0,
  },
  headerContainer: {
    width: '100%',
    paddingTop: 30, // Added 30px top padding to avoid status bar/notch overlap
  },
  headerBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center', // Center align items vertically
    paddingHorizontal: 10, // Small horizontal padding
    paddingVertical: 5, // Small vertical padding
    height: 65, // Reduced height to make header smaller
  },
  logoContainer: {
    width: 282, // Increased width by 2px
    height: 67, // Increased height by 2px
    justifyContent: 'center',
    alignItems: 'flex-start', // Align logo to the left
    marginLeft: -75, // Maintained negative left margin to keep position
    marginTop: -10, // Maintained for the header height
  },
  logo: {
    width: 272, // Increased width by 2px
    height: 70, // Increased height by 2px
    padding: 0, // No padding
  },
  backButtonContainer: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 5,
  },
  spacer: {
    flex: 1, // Takes up available space in the middle
  },
  profileIconContainer: {
    width: 38, // Width for profile icon area
    height: 38, // Height for profile icon area
    justifyContent: 'center',
    alignItems: 'center',
    padding: 0, // No padding
    margin: 0, // No margin
    marginRight: 0, // No right margin
    marginTop: -20, // Adjusted for the new header height
  },
  placeholderRight: {
    width: 38, // Same width as profile icon for balance
    height: 38,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

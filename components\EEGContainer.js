import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import EEGAnimation from './EEGAnimation';
import { useSettings } from '../contexts/SettingsContext';

/**
 * EEG Container Component
 *
 * A styled container that displays the EEG animation with a title and subtitle
 * Designed to match the blue rectangular component in the reference image
 */
const EEGContainer = ({ style }) => {
  const { darkModeEnabled } = useSettings();

  return (
    <View style={[
      styles.container,
      style
    ]}>
      {/* Main content container */}
      <View style={styles.contentContainer}>
        {/* Title and subtitle */}
        <View style={styles.textContainer}>
          <Text style={[
            styles.title,
            { color: '#FFFFFF' }
          ]}>iKlan</Text>
          <Text style={[
            styles.subtitle,
            { color: 'rgba(255, 255, 255, 0.8)' }
          ]}>Get More Ads Space For Your Needs</Text>
        </View>

        {/* Notification icon */}
        <View style={styles.notificationContainer}>
          <TouchableOpacity style={styles.notificationIcon}>
            <Ionicons name="notifications-outline" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Category cards */}
      <View style={styles.categoryContainer}>
        <View style={styles.categoryRow}>
          <View style={styles.categoryCard}>
            <View style={styles.categoryIconContainer}>
              <Ionicons name="tv-outline" size={24} color="#FF9800" />
            </View>
            <Text style={styles.categoryTitle}>Videotron</Text>
            <Text style={styles.categorySubtitle}>For video campaign</Text>
          </View>

          <View style={styles.categoryCard}>
            <View style={styles.categoryIconContainer}>
              <Ionicons name="business-outline" size={24} color="#2196F3" />
            </View>
            <Text style={styles.categoryTitle}>Billboard</Text>
            <Text style={styles.categorySubtitle}>Many ads spot for you</Text>
          </View>
        </View>

        <View style={styles.categoryRow}>
          <View style={styles.categoryCard}>
            <View style={styles.categoryIconContainer}>
              <Ionicons name="car-outline" size={24} color="#FF5722" />
            </View>
            <Text style={styles.categoryTitle}>Car Ads</Text>
            <Text style={styles.categorySubtitle}>Put your ads on car</Text>
          </View>

          <View style={styles.categoryCard}>
            <View style={styles.categoryIconContainer}>
              <Ionicons name="bicycle-outline" size={24} color="#FF9800" />
            </View>
            <Text style={styles.categoryTitle}>Motor Ads</Text>
            <Text style={styles.categorySubtitle}>Motocycle to campaign</Text>
          </View>
        </View>

        {/* Special Offer Section */}
        <View style={styles.specialOfferHeader}>
          <Text style={styles.specialOfferTitle}>Special Offer</Text>
          <TouchableOpacity>
            <Ionicons name="ellipsis-vertical" size={20} color="#333" />
          </TouchableOpacity>
        </View>

        <View style={styles.specialOfferCard}>
          <View style={styles.specialOfferContent}>
            <View style={styles.specialOfferImageContainer}>
              <View style={styles.specialOfferAvatar}>
                <Text style={styles.avatarText}>👨‍💼</Text>
              </View>
            </View>
            <View style={styles.specialOfferTextContainer}>
              <View style={styles.promotionTag}>
                <Text style={styles.promotionTagText}>PROMOTION</Text>
              </View>
              <Text style={styles.specialOfferMainText}>Let's Get Special Offer Here!</Text>
            </View>
          </View>
        </View>
      </View>

      {/* EEG Animation as background */}
      <View style={styles.animationContainer}>
        <EEGAnimation />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 500, // Increased height to accommodate all content
    borderRadius: 15,
    overflow: 'hidden',
    position: 'relative',
  },
  contentContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 2,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 14,
    opacity: 0.8,
  },
  notificationContainer: {
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
  },
  notificationIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryContainer: {
    position: 'absolute',
    top: 80,
    left: 0,
    right: 0,
    zIndex: 3,
    padding: 15,
  },
  categoryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  categoryCard: {
    width: '48%',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 4,
  },
  categorySubtitle: {
    fontSize: 12,
    color: '#666666',
  },
  specialOfferHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 15,
  },
  specialOfferTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  specialOfferCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    height: 100,
    flexDirection: 'row',
  },
  specialOfferContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  specialOfferImageContainer: {
    width: 70,
    justifyContent: 'center',
    alignItems: 'center',
  },
  specialOfferAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#4527A0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 24,
  },
  specialOfferTextContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  promotionTag: {
    backgroundColor: '#FFD54F',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    alignSelf: 'flex-start',
    marginBottom: 8,
  },
  promotionTagText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#333',
  },
  specialOfferMainText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  animationContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
});

export default EEGContainer;

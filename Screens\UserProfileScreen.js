import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  ScrollView,
  Alert,
  Image,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { useAuth } from '../contexts/AuthContext';
import SimpleImageEditor from '../components/SimpleImageEditor';

export default function UserProfileScreen({ navigation }) {
  const { currentUser, updateProfile } = useAuth();
  const [profileImage, setProfileImage] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageEditor, setShowImageEditor] = useState(false);

  useEffect(() => {
    // Load profile image if it exists
    if (currentUser?.profilePicture) {
      setProfileImage(currentUser.profilePicture);
    }
  }, [currentUser]);

  const formatDate = (dateString) => {
    if (!dateString) return 'Not provided';
    return dateString;
  };

  const requestPermission = async (permissionType) => {
    if (Platform.OS !== 'web') {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Sorry, we need camera roll permissions to make this work!',
          [{ text: 'OK' }]
        );
        return false;
      }

      if (permissionType === 'camera') {
        const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
        if (cameraStatus !== 'granted') {
          Alert.alert(
            'Permission Required',
            'Sorry, we need camera permissions to make this work!',
            [{ text: 'OK' }]
          );
          return false;
        }
      }
    }
    return true;
  };

  const pickImage = async () => {
    // Create a bottom sheet style modal instead of an alert
    // We'll use a custom component for this
    setShowImageOptions(true);
  };

  const [showImageOptions, setShowImageOptions] = useState(false);

  const removeProfilePicture = async () => {
    try {
      // Update user profile with null profile picture
      const updatedUser = {
        ...currentUser,
        profilePicture: null,
      };

      const result = await updateProfile(updatedUser);

      if (result.success) {
        setProfileImage(null);
        setShowImageOptions(false);
      } else {
        Alert.alert('Error', 'Failed to remove profile picture. Please try again.');
      }
    } catch (error) {
      console.error('Error removing profile image:', error);
      Alert.alert('Error', 'Failed to remove profile picture. Please try again.');
    }
  };

  const takePhoto = async () => {
    const hasPermission = await requestPermission('camera');
    if (!hasPermission) return;

    try {
      // First, capture the image without editing
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images'],
        allowsEditing: false, // Disable built-in editing
        quality: 0.7,
      });

      if (!result.canceled) {
        // Open the image editor with the selected image
        const imageUri = result.assets[0].uri;
        setSelectedImage(imageUri);
        setShowImageOptions(false);
        setShowImageEditor(true);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  const chooseFromGallery = async () => {
    const hasPermission = await requestPermission('gallery');
    if (!hasPermission) return;

    try {
      // First, select the image without editing
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: false, // Disable built-in editing
        quality: 0.7,
      });

      if (!result.canceled) {
        // Open the image editor with the selected image
        const imageUri = result.assets[0].uri;
        setSelectedImage(imageUri);
        setShowImageOptions(false);
        setShowImageEditor(true);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  const handleImageEditingDone = (editedImageUri) => {
    setShowImageEditor(false);
    setProfileImage(editedImageUri);
    saveProfileImage(editedImageUri);
  };

  const handleImageEditingCancel = () => {
    setShowImageEditor(false);
    setSelectedImage(null);
  };

  const saveProfileImage = async (imageUri) => {
    try {
      // Update user profile with the image URI
      const updatedUser = {
        ...currentUser,
        profilePicture: imageUri,
      };

      const result = await updateProfile(updatedUser);

      if (!result.success) {
        Alert.alert('Error', 'Failed to update profile picture. Please try again.');
      }
    } catch (error) {
      console.error('Error saving profile image:', error);
      Alert.alert('Error', 'Failed to save profile image. Please try again.');
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#ffffff" />
        </TouchableOpacity>
        <Text style={styles.title}>Profile</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Main content - Always visible */}
      <ScrollView style={styles.scrollContainer}>
        <View style={styles.profileContainer}>
          <TouchableOpacity style={styles.avatarContainer} onPress={pickImage}>
            {profileImage ? (
              <Image source={{ uri: profileImage }} style={styles.avatar} />
            ) : (
              <View style={styles.avatarPlaceholder}>
                <Text style={styles.avatarText}>
                  {currentUser?.name ? currentUser.name.charAt(0).toUpperCase() : '?'}
                </Text>
              </View>
            )}
            <View style={styles.editIconContainer}>
              <Ionicons name="camera" size={20} color="#ffffff" />
            </View>
          </TouchableOpacity>

          <Text style={styles.userName}>{currentUser?.name || 'User'}</Text>

          <View style={styles.infoCard}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Personal Information</Text>
            </View>

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Email</Text>
              <Text style={styles.infoValue}>{currentUser?.email || 'Not provided'}</Text>
            </View>

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Date of Birth</Text>
              <Text style={styles.infoValue}>{formatDate(currentUser?.dob)}</Text>
            </View>

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Gender</Text>
              <Text style={styles.infoValue}>
                {currentUser?.gender
                  ? currentUser.gender.charAt(0).toUpperCase() + currentUser.gender.slice(1)
                  : 'Not provided'}
              </Text>
            </View>

            {currentUser?.phone && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Phone</Text>
                <Text style={styles.infoValue}>{currentUser.phone}</Text>
              </View>
            )}

            {currentUser?.address && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Address</Text>
                <Text style={styles.infoValue}>{currentUser.address}</Text>
              </View>
            )}
          </View>

          <TouchableOpacity
            style={styles.editButton}
            onPress={() => navigation.navigate('EditProfile')}
          >
            <Text style={styles.editButtonText}>EDIT PROFILE</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Bottom sheet for image options */}
      {showImageOptions && (
        <View style={styles.bottomSheetOverlay}>
          <TouchableOpacity
            style={styles.bottomSheetBackdrop}
            activeOpacity={0}
            onPress={() => setShowImageOptions(false)}
          />
          <View style={styles.bottomSheet}>
            <View style={styles.bottomSheetHandle} />

            <TouchableOpacity
              style={styles.bottomSheetOption}
              onPress={() => {
                setShowImageOptions(false);
                takePhoto();
              }}
            >
              <Ionicons name="camera-outline" size={24} color="#4fc3f7" />
              <Text style={styles.bottomSheetOptionText}>Take Photo</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.bottomSheetOption}
              onPress={() => {
                setShowImageOptions(false);
                chooseFromGallery();
              }}
            >
              <Ionicons name="image-outline" size={24} color="#4fc3f7" />
              <Text style={styles.bottomSheetOptionText}>Choose from Gallery</Text>
            </TouchableOpacity>

            {currentUser?.profilePicture && (
              <TouchableOpacity
                style={styles.bottomSheetOption}
                onPress={removeProfilePicture}
              >
                <Ionicons name="trash-outline" size={24} color="#e53935" />
                <Text style={styles.bottomSheetOptionTextDelete}>Remove Current Photo</Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={[styles.bottomSheetOption, styles.cancelOption]}
              onPress={() => setShowImageOptions(false)}
            >
              <Text style={styles.cancelText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Image Editor Modal */}
      <SimpleImageEditor
        visible={showImageEditor}
        imageUri={selectedImage}
        onCancel={handleImageEditingCancel}
        onDone={handleImageEditingDone}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
    backgroundColor: '#121212',
  },
  backButton: {
    padding: 5,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  placeholder: {
    width: 34, // Same width as the back button for balanced layout
  },
  scrollContainer: {
    flex: 1,
  },
  profileContainer: {
    padding: 20,
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 20,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  avatarPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#666666', // Changed from blue to dark grey
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  editIconContainer: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#666666', // Changed from blue to dark grey
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#121212',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 30,
  },
  infoCard: {
    backgroundColor: '#1e1e1e',
    borderRadius: 10,
    padding: 20,
    width: '100%',
    marginBottom: 30,
  },
  sectionHeader: {
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
    paddingBottom: 15,
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4fc3f7',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  infoLabel: {
    fontSize: 16,
    color: '#999999',
  },
  infoValue: {
    fontSize: 16,
    color: '#ffffff',
    textAlign: 'right',
    flex: 1,
    marginLeft: 20,
  },
  editButton: {
    backgroundColor: '#4fc3f7',
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 30,
    alignItems: 'center',
    marginBottom: 30,
  },
  editButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  settingsButton: {
    backgroundColor: '#333333',
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 30,
    alignItems: 'center',
    marginBottom: 30,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  settingsButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  settingsIcon: {
    marginRight: 10,
  },
  // Bottom sheet styles
  bottomSheetOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    pointerEvents: 'box-none', // This allows touches to pass through to the content underneath
  },
  bottomSheetBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black overlay to create contrast
  },
  bottomSheet: {
    backgroundColor: '#252525', // Slightly lighter than the main background for contrast
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: Platform.OS === 'ios' ? 40 : 0, // Account for iOS home indicator
    width: '100%',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    elevation: 15, // Increased elevation for Android shadow
    shadowColor: '#000', // For iOS shadow
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.4,
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: '#333333', // Subtle border for better definition
  },
  bottomSheetHandle: {
    width: 50,
    height: 5,
    backgroundColor: '#888', // Lighter color for better visibility
    borderRadius: 3,
    alignSelf: 'center',
    marginTop: 12,
    marginBottom: 12,
  },
  bottomSheetOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 20, // Further increased padding for better touch targets
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
    backgroundColor: '#2a2a2a', // Slightly lighter than the bottom sheet background
  },
  bottomSheetOptionText: {
    color: '#ffffff',
    fontSize: 16,
    marginLeft: 15,
  },
  bottomSheetOptionTextDelete: {
    color: '#e53935', // Red color for delete option
    fontSize: 16,
    marginLeft: 15,
  },
  cancelOption: {
    justifyContent: 'center',
    borderBottomWidth: 0,
    paddingVertical: 22, // Further increased padding for the cancel button
    marginBottom: 5, // Add a bit of margin at the bottom
    backgroundColor: '#222', // Darker than other options to make it stand out
  },
  cancelText: {
    color: '#4fc3f7',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

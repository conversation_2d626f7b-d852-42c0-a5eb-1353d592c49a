// This file helps debug polyfill issues
// Import it in index.js before any other imports to check if polyfills are working

// Check if Function.prototype.bind exists
console.log('Function.prototype.bind exists:', typeof Function.prototype.bind === 'function');

// Check if Function exists
console.log('Function exists:', typeof Function === 'function');

// Check if global.Function exists
console.log('global.Function exists:', typeof global.Function === 'function');

// Check if Function.prototype exists
console.log('Function.prototype exists:', Function.prototype !== undefined);

// Export a dummy function to avoid tree-shaking
export default function debugPolyfills() {
  return 'Polyfills debugging complete';
}

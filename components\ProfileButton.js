import React from 'react';
import { TouchableOpacity, Image, StyleSheet, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import { useSettings } from '../contexts/SettingsContext';

export default function ProfileButton({ onPress }) {
  const { currentUser } = useAuth();
  const { darkModeEnabled } = useSettings();

  return (
    <TouchableOpacity
      style={styles.profileButton}
      onPress={onPress}
      hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }} // Increase touch area without affecting layout
    >
      {currentUser?.profilePicture ? (
        <Image
          source={{ uri: currentUser.profilePicture }}
          style={styles.profileImage}
        />
      ) : (
        <Ionicons
          name="person-circle"
          size={38}
          color={darkModeEnabled ? "#cccccc" : "#666666"} // Light white in dark mode, dark grey in light mode
        />
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  profileButton: {
    padding: 0,
    margin: 0,
    width: 38,
    height: 38,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImage: {
    width: 38,
    height: 38,
    borderRadius: 19,
  },
});

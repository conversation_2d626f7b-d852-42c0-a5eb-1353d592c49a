# Fixing Permissions for EAS Build

This document explains how to fix permission issues when building your Expo/React Native app with EAS Build.

## The Problem

When building with EAS, you might encounter permission errors because EAS needs to read all files in your project. This is especially common on Windows systems where file permissions can be more restrictive by default.

Common error messages include:
- "EACCES: permission denied"
- "Error: spawn EPERM"
- "Access is denied"
- "The system cannot find the path specified"

## The Solution

We've created a comprehensive solution with two scripts:

1. **fix-permissions.bat** - A simple batch file that runs the PowerShell script with the correct execution policy
2. **fix-permissions.ps1** - A PowerShell script that:
   - Sets read and execute permissions for all files in your project directory
   - Ensures app.json exists (creates it from app.json.bak if needed)
   - Checks for essential files and configurations
   - Verifies EAS login status
   - Skips node_modules and .git directories to avoid unnecessary permission changes

## How to Use

### Option 1: Using the Batch File (Recommended)

1. **Double-click the `fix-permissions.bat` file**

   This will automatically run the PowerShell script with the correct execution policy.

### Option 2: Using PowerShell Directly

1. **Run PowerShell as Administrator**

   Right-click on PowerShell and select "Run as Administrator", then navigate to your project directory and run:

   ```powershell
   Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass
   .\fix-permissions.ps1
   ```

### After Running the Script

1. **Review the output**

   The script will display information about:
   - Whether it's running as Administrator
   - Essential files that were found or are missing
   - EAS login status
   - Any permission errors that occurred

2. **Run EAS Build**

   After fixing permissions, you can run:

   ```
   npx eas build --platform android --profile development
   ```

   Or for iOS:

   ```
   npx eas build --platform ios --profile development
   ```

## Complete Checklist for EAS Build

Before building with EAS, ensure you have:

1. **Required Files**
   - [x] app.json with correct configuration
   - [x] eas.json with build profiles
   - [x] package.json with dependencies

2. **EAS Setup**
   - [x] Installed EAS CLI: `npm install -g eas-cli`
   - [x] Logged in to EAS: `eas login`
   - [x] Configured build profiles in eas.json

3. **Android-specific Requirements**
   - [x] Valid package name in app.json (e.g., "com.synaptix.app")
   - [x] Required permissions listed in app.json

4. **iOS-specific Requirements**
   - [x] Valid bundleIdentifier in app.json (e.g., "com.synaptix.app")
   - [x] Apple Developer account connected to EAS (for iOS builds)

## Troubleshooting

If you still encounter permission issues:

1. **Run as Administrator**

   Make sure you're running the script as Administrator to ensure it has the necessary permissions.

2. **Check for locked files**

   Some files might be locked by other processes. Close any editors or processes that might be using project files.

3. **Manual permission setting**

   You can manually set permissions for specific files that might be causing issues:

   ```powershell
   $acl = Get-Acl "path\to\problematic\file"
   $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("Everyone", "ReadAndExecute", "Allow")
   $acl.SetAccessRule($accessRule)
   Set-Acl "path\to\problematic\file" $acl
   ```

4. **Verify EAS CLI is up to date**
   ```
   npm install -g eas-cli@latest
   ```

5. **Check EAS account status**
   ```
   eas whoami
   ```

6. **Verify app.json structure**

   Make sure your app.json has the correct structure with all required fields.

7. **Check for hidden files**

   Some hidden files might need permissions set. Run the script with Administrator privileges to ensure all files are processed.

8. **Reinstall node_modules**

   If you're still having issues, try reinstalling your dependencies:
   ```
   rm -rf node_modules
   npm install
   ```

## Additional Resources

- [Expo EAS Build Documentation](https://docs.expo.dev/build/introduction/)
- [Troubleshooting EAS Build](https://docs.expo.dev/build/troubleshooting/)
- [Expo App Configuration](https://docs.expo.dev/workflow/configuration/)
- [EAS Build for Android](https://docs.expo.dev/build-reference/android/)
- [EAS Build for iOS](https://docs.expo.dev/build-reference/ios/)

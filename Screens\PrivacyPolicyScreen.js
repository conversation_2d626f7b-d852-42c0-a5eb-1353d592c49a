import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import CustomHeader from '../components/CustomHeader';
import { ensureStatusBarAppearance } from '../utils/screenTransition';
import { useSettings } from '../contexts/SettingsContext';

export default function PrivacyPolicyScreen() {
  const navigation = useNavigation();
  const { darkModeEnabled } = useSettings();

  // Ensure StatusBar is properly configured when the screen mounts
  useEffect(() => {
    ensureStatusBarAppearance(darkModeEnabled);
  }, [darkModeEnabled]);

  const handleBackPress = () => {
    navigation.goBack();
  };

  return (
    <View style={[
      styles.container,
      { backgroundColor: darkModeEnabled ? '#121212' : '#f5f5f5' }
    ]}>
      <StatusBar barStyle={darkModeEnabled ? "light-content" : "dark-content"} />
      <CustomHeader
        title="Privacy Policy"
        showProfileIcon={false}
        showBackButton={true}
        onBackPress={handleBackPress} />

      <ScrollView style={styles.scrollContainer}>
        <View style={styles.contentContainer}>
          <View style={[
            styles.card,
            { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
          ]}>
            <Text style={[
              styles.paragraph,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              Synaptix is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our app that collects your EEG signal and analyzes it for abnormalities.
            </Text>

            <Text style={[
              styles.sectionTitle,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>1. Information We Collect</Text>
            <Text style={[
              styles.paragraph,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              We may collect the following information:
            </Text>
            <View style={styles.bulletList}>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• EEG Signal Data: Brainwave data collected via EEG hardware.</Text>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• Device Information: Device model, OS version, and hardware used for EEG collection.</Text>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• Usage Data: Interactions within the app (e.g., analysis results).</Text>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• Optional Data: Name, email (if user creates an account or contacts us).</Text>
            </View>

            <Text style={[
              styles.sectionTitle,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>2. How We Use Your Information</Text>
            <Text style={[
              styles.paragraph,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              We use the collected data to:
            </Text>
            <View style={styles.bulletList}>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• Analyze your EEG signal using our dataset.</Text>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• Determine if your signal is within a normal range.</Text>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• Improve our machine learning models.</Text>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• Provide customer support or respond to inquiries.</Text>
            </View>

            <Text style={[
              styles.sectionTitle,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>3. Data Sharing and Disclosure</Text>
            <Text style={[
              styles.paragraph,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              We do not sell or rent your data. We may share anonymized data:
            </Text>
            <View style={styles.bulletList}>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• With trusted third-party services that help improve app performance.</Text>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• When required by law or for the protection of rights and safety.</Text>
            </View>

            <Text style={[
              styles.sectionTitle,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>4. Data Security</Text>
            <Text style={[
              styles.paragraph,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              We implement reasonable administrative, technical, and physical safeguards to protect your data.
            </Text>

            <Text style={[
              styles.sectionTitle,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>5. Data Retention</Text>
            <Text style={[
              styles.paragraph,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              Your data is retained only as long as necessary for analysis and legal compliance, unless you request its deletion.
            </Text>

            <Text style={[
              styles.sectionTitle,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>6. Your Rights</Text>
            <Text style={[
              styles.paragraph,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              Depending on your location, you may have rights to:
            </Text>
            <View style={styles.bulletList}>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• Access or delete your data.</Text>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• Withdraw consent.</Text>
              <Text style={[
                styles.bulletItem,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>• File a complaint with a data protection authority.</Text>
            </View>
            <Text style={[
              styles.paragraph,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              To exercise these rights, contact us at: <EMAIL>
            </Text>

            <Text style={[
              styles.sectionTitle,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>7. Children's Privacy</Text>
            <Text style={[
              styles.paragraph,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              This app is not intended for children under 13 (or applicable age under local laws). We do not knowingly collect data from minors.
            </Text>

            <Text style={[
              styles.sectionTitle,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>8. Changes to This Policy</Text>
            <Text style={[
              styles.paragraph,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              We may update this policy. Any changes will be posted with an updated date.
            </Text>

            <Text style={[
              styles.contactSection,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              Contact Us:
            </Text>
            <Text style={[
              styles.paragraph,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              For questions or concerns, email <NAME_EMAIL>
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  card: {
    borderRadius: 10,
    padding: 20,
  },
  effectiveDate: {
    fontSize: 14,
    marginBottom: 20,
    fontStyle: 'italic',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 25,
    marginBottom: 10,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 15,
  },
  bulletList: {
    marginLeft: 10,
    marginBottom: 15,
  },
  bulletItem: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 8,
  },
  contactSection: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 30,
    marginBottom: 10,
  },
});

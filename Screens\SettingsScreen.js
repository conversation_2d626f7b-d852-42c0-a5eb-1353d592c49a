import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  ScrollView,
  Switch,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import CustomHeader from '../components/CustomHeader';
import { useSettings } from '../contexts/SettingsContext';

export default function SettingsScreen() {
  const navigation = useNavigation();
  const {
    // Settings states
    notificationsEnabled,
    darkModeEnabled,
    dataBackupEnabled,

    // Loading states
    notificationsLoading,
    darkModeLoading,
    dataBackupLoading,

    // Toggle functions
    toggleNotifications,
    toggleDarkMode,
    toggleDataBackup
  } = useSettings();

  const handleBackPress = () => {
    navigation.goBack();
  };

  const handleToggleNotifications = async (value) => {
    // Don't allow toggling if already in progress
    if (notificationsLoading) return;

    const success = await toggleNotifications(value);
    if (!success && value) {
      Alert.alert(
        "Notification Permission",
        "Please enable notifications in your device settings to receive updates.",
        [{ text: "OK" }]
      );
    }
  };

  const handleToggleDarkMode = async (value) => {
    // Don't allow toggling if already in progress
    if (darkModeLoading) return;

    await toggleDarkMode(value);
  };

  const handleToggleDataBackup = async (value) => {
    // Don't allow toggling if already in progress
    if (dataBackupLoading) return;

    await toggleDataBackup(value);
  };

  return (
    <View style={[
      styles.container,
      { backgroundColor: darkModeEnabled ? '#121212' : '#f5f5f5' }
    ]}>
      <StatusBar barStyle={darkModeEnabled ? "light-content" : "dark-content"} />
      <CustomHeader
        title="Settings"
        showProfileIcon={false}
        showBackButton={true}
        onBackPress={handleBackPress} />

      <ScrollView style={styles.scrollContainer}>
        <View style={styles.settingsContainer}>
          <View style={[
            styles.settingsCard,
            { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
          ]}>
            <View style={[
              styles.sectionHeader,
              { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
            ]}>
              <Text style={[
                styles.sectionTitle,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>App Settings</Text>
            </View>

            <View style={[
              styles.settingRow,
              { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
            ]}>
              <View style={styles.settingInfo}>
                <Text style={[
                  styles.settingLabel,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>Notifications</Text>
                <Text style={[
                  styles.settingDescription,
                  { color: darkModeEnabled ? '#999999' : '#666666' }
                ]}>Receive alerts and updates</Text>
              </View>
              <Switch
                value={notificationsEnabled}
                onValueChange={handleToggleNotifications}
                trackColor={{ false: darkModeEnabled ? '#333' : '#cccccc', true: darkModeEnabled ? '#ffffff' : '#d0d0d0' }}
                thumbColor={notificationsEnabled ? (darkModeEnabled ? '#9e9e9e' : '#8a8a8a') : (darkModeEnabled ? '#666666' : '#f4f3f4')}
                disabled={notificationsLoading}
              />
            </View>

            <View style={[
              styles.settingRow,
              { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
            ]}>
              <View style={styles.settingInfo}>
                <Text style={[
                  styles.settingLabel,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>Dark Mode</Text>
                <Text style={[
                  styles.settingDescription,
                  { color: darkModeEnabled ? '#999999' : '#666666' }
                ]}>Use dark theme throughout the app</Text>
              </View>
              <Switch
                value={darkModeEnabled}
                onValueChange={handleToggleDarkMode}
                trackColor={{ false: darkModeEnabled ? '#333' : '#cccccc', true: darkModeEnabled ? '#ffffff' : '#d0d0d0' }}
                thumbColor={darkModeEnabled ? (darkModeEnabled ? '#9e9e9e' : '#8a8a8a') : (darkModeEnabled ? '#666666' : '#f4f3f4')}
                disabled={darkModeLoading}
              />
            </View>

            <View style={[
              styles.settingRow,
              { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
            ]}>
              <View style={styles.settingInfo}>
                <Text style={[
                  styles.settingLabel,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>Data Backup</Text>
                <Text style={[
                  styles.settingDescription,
                  { color: darkModeEnabled ? '#999999' : '#666666' }
                ]}>Automatically backup your data</Text>
              </View>
              <Switch
                value={dataBackupEnabled}
                onValueChange={handleToggleDataBackup}
                trackColor={{ false: darkModeEnabled ? '#333' : '#cccccc', true: darkModeEnabled ? '#ffffff' : '#d0d0d0' }}
                thumbColor={dataBackupEnabled ? (darkModeEnabled ? '#9e9e9e' : '#8a8a8a') : (darkModeEnabled ? '#666666' : '#f4f3f4')}
                disabled={dataBackupLoading}
              />
            </View>
          </View>

          <View style={[
            styles.settingsCard,
            { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
          ]}>
            <View style={[
              styles.sectionHeader,
              { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
            ]}>
              <Text style={[
                styles.sectionTitle,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>About</Text>
            </View>

            <TouchableOpacity
              style={[
                styles.aboutRow,
                { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
              ]}
              onPress={() => navigation.navigate('PrivacyPolicy')}
            >
              <Text style={[
                styles.aboutLabel,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>Privacy Policy</Text>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={darkModeEnabled ? "#666" : "#999"}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.aboutRow,
                { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
              ]}
              onPress={() => navigation.navigate('TermsOfService')}
            >
              <Text style={[
                styles.aboutLabel,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>Terms of Service</Text>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={darkModeEnabled ? "#666" : "#999"}
              />
            </TouchableOpacity>

            <TouchableOpacity style={[
              styles.aboutRow,
              { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
            ]}>
              <Text style={[
                styles.aboutLabel,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>App Version</Text>
              <Text style={[
                styles.versionText,
                { color: darkModeEnabled ? '#999999' : '#666666' }
              ]}>1.0.0</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  settingsContainer: {
    padding: 20,
  },
  settingsCard: {
    borderRadius: 10,
    padding: 20,
    marginBottom: 20,
  },
  sectionHeader: {
    borderBottomWidth: 1,
    paddingBottom: 15,
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
  },
  settingInfo: {
    flex: 1,
  },
  settingLabel: {
    fontSize: 16,
    marginBottom: 5,
  },
  settingDescription: {
    fontSize: 14,
  },
  aboutRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
  },
  aboutLabel: {
    fontSize: 16,
  },
  versionText: {
    fontSize: 14,
  },

});

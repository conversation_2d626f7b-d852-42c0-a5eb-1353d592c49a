import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Modal,
  Dimensions,
  ActivityIndicator,
  Platform,
  SafeAreaView,
  ScrollView,
  Alert,
  PanResponder,
  Animated,
} from 'react-native';
import * as ImageManipulator from 'expo-image-manipulator';
import { Ionicons } from '@expo/vector-icons';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

const ImageEditor = ({ visible, imageUri, onCancel, onDone }) => {
  const [processing, setProcessing] = useState(false);
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
  const [rotation, setRotation] = useState(0); // 0, 90, 180, 270 degrees
  const [flipHorizontal, setFlipHorizontal] = useState(false);
  const [flipVertical, setFlipVertical] = useState(false);
  const [editedImageUri, setEditedImageUri] = useState(null);
  const [activeTab, setActiveTab] = useState('crop'); // 'crop', 'rotate', 'flip'

  // Custom crop state variables
  const [cropAspectRatio, setCropAspectRatio] = useState(null); // null = free form, 1 = square, 16/9 = 16:9, etc.
  const [cropPosition, setCropPosition] = useState({ x: 0, y: 0 });
  const [cropSize, setCropSize] = useState({ width: 300, height: 300 });
  const [initialCropBoxSize, setInitialCropBoxSize] = useState({ width: 300, height: 300 });

  // Animated values for the crop box
  const cropBoxPosition = useRef(new Animated.ValueXY({ x: 0, y: 0 })).current;
  const cropBoxSize = useRef(new Animated.ValueXY({ x: 300, y: 300 })).current;

  // Load image dimensions when the component mounts or imageUri changes
  useEffect(() => {
    if (imageUri) {
      Image.getSize(imageUri, (width, height) => {
        setImageSize({ width, height });

        // Initialize the crop box after setting image size
        setTimeout(() => {
          initializeCropBox();
        }, 100);
      });

      // Reset editing state when a new image is loaded
      setRotation(0);
      setFlipHorizontal(false);
      setFlipVertical(false);
      setEditedImageUri(null);
      setActiveTab('crop');
      setCropAspectRatio(null); // Always use free-form cropping
    }
  }, [imageUri]);

  // Apply all transformations and save the result
  const applyTransformations = async () => {
    if (!imageUri) return;

    setProcessing(true);

    try {
      // Build the array of operations to perform
      const operations = [];

      // Always apply crop if the user has adjusted the crop box
      // This ensures proper coordinates for subsequent operations
      if (activeTab === 'crop') {
        try {
          // Get the current dimensions of the displayed image
          // We need to account for the image's aspect ratio and how it's displayed
          const imageAspectRatio = imageSize.width / imageSize.height;

          // Calculate the dimensions of the image as displayed on screen
          let displayedImageWidth, displayedImageHeight;

          if (imageAspectRatio >= 1) {
            // Landscape or square image
            displayedImageWidth = Math.min(SCREEN_WIDTH, SCREEN_HEIGHT * 0.5 * imageAspectRatio);
            displayedImageHeight = displayedImageWidth / imageAspectRatio;
          } else {
            // Portrait image
            displayedImageHeight = Math.min(SCREEN_HEIGHT * 0.5, SCREEN_WIDTH / imageAspectRatio);
            displayedImageWidth = displayedImageHeight * imageAspectRatio;
          }

          // Calculate the scale factors between displayed and actual image
          const scaleX = imageSize.width / displayedImageWidth;
          const scaleY = imageSize.height / displayedImageHeight;

          // Calculate the offset of the image on screen (for centering)
          const imageOffsetX = (SCREEN_WIDTH - displayedImageWidth) / 2;
          const imageOffsetY = (SCREEN_HEIGHT * 0.5 - displayedImageHeight) / 2;

          // Adjust crop position to account for image offset
          const adjustedCropX = cropPosition.x - imageOffsetX;
          const adjustedCropY = cropPosition.y - imageOffsetY;

          // Convert crop box coordinates to actual image coordinates
          // Ensure we don't go out of bounds
          const originX = Math.max(0, adjustedCropX * scaleX);
          const originY = Math.max(0, adjustedCropY * scaleY);

          // Ensure crop dimensions don't exceed image boundaries
          const cropWidth = Math.min(
            imageSize.width - originX,
            cropSize.width * scaleX
          );
          const cropHeight = Math.min(
            imageSize.height - originY,
            cropSize.height * scaleY
          );

          // Only add crop operation if dimensions are valid
          if (cropWidth > 10 && cropHeight > 10) {
            operations.push({
              crop: {
                originX: originX,
                originY: originY,
                width: cropWidth,
                height: cropHeight,
              },
            });
          }
        } catch (error) {
          console.error('Error calculating crop coordinates:', error);
        }
      }

      // Add rotation if needed
      if (rotation !== 0) {
        operations.push({ rotate: rotation });
      }

      // Add flip operations if needed
      if (flipHorizontal) {
        operations.push({ flip: { horizontal: true } });
      }

      if (flipVertical) {
        operations.push({ flip: { vertical: true } });
      }

      // Perform all operations
      const manipResult = await ImageManipulator.manipulateAsync(
        imageUri,
        operations,
        { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG }
      );

      // Save the edited image URI
      setEditedImageUri(manipResult.uri);

      return manipResult.uri;
    } catch (error) {
      console.error('Error applying transformations:', error);
      return null;
    } finally {
      setProcessing(false);
    }
  };

  // Preview the transformations without saving
  const previewTransformations = async () => {
    const result = await applyTransformations();
    if (result) {
      setEditedImageUri(result);
    }
  };

  // Apply transformations and finish editing
  const handleDone = async () => {
    // If no preview has been generated yet, show a confirmation dialog
    if (!editedImageUri && (rotation !== 0 || flipHorizontal || flipVertical || activeTab === 'crop')) {
      Alert.alert(
        'Apply Changes?',
        'You have not previewed your changes. Would you like to apply them now?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Apply Changes',
            onPress: async () => {
              const finalImageUri = await applyTransformations();
              if (finalImageUri && onDone) {
                onDone(finalImageUri);
              }
            },
          },
        ],
        { cancelable: true }
      );
    } else {
      // If preview exists or no changes made, just apply and finish
      const finalImageUri = editedImageUri || imageUri;
      if (finalImageUri && onDone) {
        onDone(finalImageUri);
      }
    }
  };

  // Rotate the image left (counter-clockwise)
  const rotateLeft = () => {
    setRotation((prev) => {
      // Handle negative rotation correctly
      const newRotation = prev - 90;
      return newRotation < 0 ? newRotation + 360 : newRotation;
    });
    if (editedImageUri) setEditedImageUri(null);
  };

  // Rotate the image right (clockwise)
  const rotateRight = () => {
    setRotation((prev) => (prev + 90) % 360);
    if (editedImageUri) setEditedImageUri(null);
  };

  // Flip the image horizontally
  const toggleFlipHorizontal = () => {
    setFlipHorizontal((prev) => !prev);
    if (editedImageUri) setEditedImageUri(null);
  };

  // Flip the image vertically
  const toggleFlipVertical = () => {
    setFlipVertical((prev) => !prev);
    if (editedImageUri) setEditedImageUri(null);
  };

  // Initialize crop box with free-form cropping
  const initializeCropBox = () => {
    // Always use free-form cropping
    setCropAspectRatio(null);

    // Calculate image boundaries
    const imageAspectRatio = imageSize.width / imageSize.height;
    let displayedImageWidth, displayedImageHeight;

    if (imageAspectRatio >= 1) {
      // Landscape or square image
      displayedImageWidth = Math.min(SCREEN_WIDTH, SCREEN_HEIGHT * 0.5 * imageAspectRatio);
      displayedImageHeight = displayedImageWidth / imageAspectRatio;
    } else {
      // Portrait image
      displayedImageHeight = Math.min(SCREEN_HEIGHT * 0.5, SCREEN_WIDTH / imageAspectRatio);
      displayedImageWidth = displayedImageHeight * imageAspectRatio;
    }

    // Calculate initial crop box size (70% of the displayed image)
    const initialWidth = Math.round(displayedImageWidth * 0.7);
    const initialHeight = Math.round(displayedImageHeight * 0.7);

    // Center the crop box
    const imageOffsetX = (SCREEN_WIDTH - displayedImageWidth) / 2;
    const imageOffsetY = (SCREEN_HEIGHT * 0.5 - displayedImageHeight) / 2;
    const centerX = imageOffsetX + (displayedImageWidth - initialWidth) / 2;
    const centerY = imageOffsetY + (displayedImageHeight - initialHeight) / 2;

    // Update crop size and position
    setCropSize({ width: initialWidth, height: initialHeight });
    setCropPosition({ x: centerX, y: centerY });
    cropBoxSize.setValue({ x: initialWidth, y: initialHeight });
    cropBoxPosition.setValue({ x: centerX, y: centerY });
  };

  // Create pan responders for each corner and the whole crop box
  const createCornerPanResponder = (corner) => {
    return PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        // Store current values as offsets
        cropBoxPosition.setOffset({
          x: cropBoxPosition.x._value,
          y: cropBoxPosition.y._value
        });
        cropBoxSize.setOffset({
          x: cropBoxSize.x._value,
          y: cropBoxSize.y._value
        });
      },
      onPanResponderMove: (_, gesture) => {
        // Get current values
        let newWidth = cropBoxSize.x._offset;
        let newHeight = cropBoxSize.y._offset;
        let newX = cropBoxPosition.x._offset;
        let newY = cropBoxPosition.y._offset;

        // Calculate image boundaries
        const imageAspectRatio = imageSize.width / imageSize.height;
        let displayedImageWidth, displayedImageHeight;

        if (imageAspectRatio >= 1) {
          // Landscape or square image
          displayedImageWidth = Math.min(SCREEN_WIDTH, SCREEN_HEIGHT * 0.5 * imageAspectRatio);
          displayedImageHeight = displayedImageWidth / imageAspectRatio;
        } else {
          // Portrait image
          displayedImageHeight = Math.min(SCREEN_HEIGHT * 0.5, SCREEN_WIDTH / imageAspectRatio);
          displayedImageWidth = displayedImageHeight * imageAspectRatio;
        }

        const imageOffsetX = (SCREEN_WIDTH - displayedImageWidth) / 2;
        const imageOffsetY = (SCREEN_HEIGHT * 0.5 - displayedImageHeight) / 2;

        // Handle different corners with boundary checks
        switch (corner) {
          case 'topLeft':
            newWidth -= gesture.dx;
            newHeight -= gesture.dy;
            newX += gesture.dx;
            newY += gesture.dy;
            break;
          case 'topRight':
            newWidth += gesture.dx;
            newHeight -= gesture.dy;
            newY += gesture.dy;
            break;
          case 'bottomLeft':
            newWidth -= gesture.dx;
            newHeight += gesture.dy;
            newX += gesture.dx;
            break;
          case 'bottomRight':
            newWidth += gesture.dx;
            newHeight += gesture.dy;
            break;
        }

        // Enforce minimum size
        const minSize = 80;
        if (newWidth < minSize) {
          newWidth = minSize;
          if (corner === 'topLeft' || corner === 'bottomLeft') {
            newX = cropBoxPosition.x._offset + (cropBoxSize.x._offset - minSize);
          }
        }

        if (newHeight < minSize) {
          newHeight = minSize;
          if (corner === 'topLeft' || corner === 'topRight') {
            newY = cropBoxPosition.y._offset + (cropBoxSize.y._offset - minSize);
          }
        }

        // No aspect ratio enforcement - free-form cropping

        // Ensure crop box stays within image boundaries
        // Right edge
        const rightEdge = imageOffsetX + displayedImageWidth;
        if (newX + newWidth > rightEdge) {
          if (corner === 'topRight' || corner === 'bottomRight') {
            newWidth = rightEdge - newX;

          } else {
            newX = rightEdge - newWidth;
          }
        }

        // Bottom edge
        const bottomEdge = imageOffsetY + displayedImageHeight;
        if (newY + newHeight > bottomEdge) {
          if (corner === 'bottomLeft' || corner === 'bottomRight') {
            newHeight = bottomEdge - newY;

          } else {
            newY = bottomEdge - newHeight;
          }
        }

        // Left edge
        if (newX < imageOffsetX) {
          newX = imageOffsetX;
          if (corner === 'topLeft' || corner === 'bottomLeft') {
            newWidth = cropBoxPosition.x._offset + cropBoxSize.x._offset - imageOffsetX;

          }
        }

        // Top edge
        if (newY < imageOffsetY) {
          newY = imageOffsetY;
          if (corner === 'topLeft' || corner === 'topRight') {
            newHeight = cropBoxPosition.y._offset + cropBoxSize.y._offset - imageOffsetY;

          }
        }

        // Update animated values
        cropBoxSize.setValue({ x: newWidth, y: newHeight });
        cropBoxPosition.setValue({ x: newX, y: newY });

        // Update state
        setCropSize({ width: newWidth, height: newHeight });
        setCropPosition({ x: newX, y: newY });
      },
      onPanResponderRelease: () => {
        cropBoxPosition.flattenOffset();
        cropBoxSize.flattenOffset();

        // Clear any preview when crop box changes
        if (editedImageUri) {
          setEditedImageUri(null);
        }
      }
    });
  };

  // Create pan responder for moving the entire crop box
  const movePanResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,
    onPanResponderGrant: () => {
      cropBoxPosition.setOffset({
        x: cropBoxPosition.x._value,
        y: cropBoxPosition.y._value
      });
    },
    onPanResponderMove: (_, gesture) => {
      // Calculate image boundaries
      const imageAspectRatio = imageSize.width / imageSize.height;
      let displayedImageWidth, displayedImageHeight;

      if (imageAspectRatio >= 1) {
        // Landscape or square image
        displayedImageWidth = Math.min(SCREEN_WIDTH, SCREEN_HEIGHT * 0.5 * imageAspectRatio);
        displayedImageHeight = displayedImageWidth / imageAspectRatio;
      } else {
        // Portrait image
        displayedImageHeight = Math.min(SCREEN_HEIGHT * 0.5, SCREEN_WIDTH / imageAspectRatio);
        displayedImageWidth = displayedImageHeight * imageAspectRatio;
      }

      const imageOffsetX = (SCREEN_WIDTH - displayedImageWidth) / 2;
      const imageOffsetY = (SCREEN_HEIGHT * 0.5 - displayedImageHeight) / 2;

      // Calculate new position
      let newX = cropBoxPosition.x._offset + gesture.dx;
      let newY = cropBoxPosition.y._offset + gesture.dy;

      // Ensure crop box stays within image boundaries
      // Right edge
      if (newX + cropBoxSize.x._value > imageOffsetX + displayedImageWidth) {
        newX = imageOffsetX + displayedImageWidth - cropBoxSize.x._value;
      }

      // Bottom edge
      if (newY + cropBoxSize.y._value > imageOffsetY + displayedImageHeight) {
        newY = imageOffsetY + displayedImageHeight - cropBoxSize.y._value;
      }

      // Left edge
      if (newX < imageOffsetX) {
        newX = imageOffsetX;
      }

      // Top edge
      if (newY < imageOffsetY) {
        newY = imageOffsetY;
      }

      // Update animated value
      cropBoxPosition.setValue({
        x: newX,
        y: newY
      });

      // Update state
      setCropPosition({
        x: newX,
        y: newY
      });
    },
    onPanResponderRelease: () => {
      cropBoxPosition.flattenOffset();

      // Clear any preview when crop box changes
      if (editedImageUri) {
        setEditedImageUri(null);
      }
    }
  });

  // Create pan responders for each corner
  const topLeftPanResponder = createCornerPanResponder('topLeft');
  const topRightPanResponder = createCornerPanResponder('topRight');
  const bottomLeftPanResponder = createCornerPanResponder('bottomLeft');
  const bottomRightPanResponder = createCornerPanResponder('bottomRight');

  if (!visible || !imageUri) return null;

  return (
    <Modal
      visible={visible}
      transparent={false}
      animationType="slide"
      onRequestClose={onCancel}
      statusBarTranslucent={true}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.headerButton} onPress={onCancel}>
            <Text style={styles.headerButtonText}>Cancel</Text>
          </TouchableOpacity>

          <Text style={styles.headerTitle}>Edit Photo</Text>

          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleDone}
            disabled={processing}
          >
            <Text style={[styles.headerButtonText, styles.doneButton]}>Done</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.imageContainer}>
          {processing ? (
            <ActivityIndicator size="large" color="#ffffff" />
          ) : (
            <>
              <Image
                source={{ uri: editedImageUri || imageUri }}
                style={[
                  styles.image,
                  // Apply transform styles for preview (rotation and flip)
                  {
                    transform: [
                      { rotate: `${rotation}deg` },
                      { scaleX: flipHorizontal ? -1 : 1 },
                      { scaleY: flipVertical ? -1 : 1 }
                    ]
                  }
                ]}
                resizeMode="contain"
              />

              {/* Only show crop overlay when in crop mode */}
              {activeTab === 'crop' && (
                <View style={styles.cropOverlay}>
                  <Animated.View
                    style={[
                      styles.cropBox,
                      {
                        width: cropBoxSize.x,
                        height: cropBoxSize.y,
                        transform: [
                          { translateX: cropBoxPosition.x },
                          { translateY: cropBoxPosition.y }
                        ]
                      }
                    ]}
                    {...movePanResponder.panHandlers}
                  >
                    {/* Draggable corners */}
                    <View
                      style={styles.cropCorner}
                      {...topLeftPanResponder.panHandlers}
                    />
                    <View
                      style={[styles.cropCorner, styles.topRight]}
                      {...topRightPanResponder.panHandlers}
                    />
                    <View
                      style={[styles.cropCorner, styles.bottomLeft]}
                      {...bottomLeftPanResponder.panHandlers}
                    />
                    <View
                      style={[styles.cropCorner, styles.bottomRight]}
                      {...bottomRightPanResponder.panHandlers}
                    />

                    {/* Grid lines for better visual guidance */}
                    <View style={styles.gridLine} />
                    <View style={[styles.gridLine, styles.horizontalGridLine]} />
                    <View style={[styles.gridLine, styles.verticalGridLine]} />
                  </Animated.View>
                </View>
              )}
            </>
          )}
        </View>

        {/* Tabs for different editing modes */}
        <View style={styles.tabsContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'crop' && styles.activeTab]}
            onPress={() => setActiveTab('crop')}
          >
            <Ionicons name="crop-outline" size={24} color={activeTab === 'crop' ? "#ffffff" : "#ffffff"} />
            <Text style={[styles.tabText, activeTab === 'crop' && styles.activeTabText]}>Crop</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'rotate' && styles.activeTab]}
            onPress={() => setActiveTab('rotate')}
          >
            <Ionicons name="refresh-outline" size={24} color={activeTab === 'rotate' ? "#ffffff" : "#ffffff"} />
            <Text style={[styles.tabText, activeTab === 'rotate' && styles.activeTabText]}>Rotate</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'flip' && styles.activeTab]}
            onPress={() => setActiveTab('flip')}
          >
            <Ionicons name="swap-horizontal-outline" size={24} color={activeTab === 'flip' ? "#ffffff" : "#ffffff"} />
            <Text style={[styles.tabText, activeTab === 'flip' && styles.activeTabText]}>Flip</Text>
          </TouchableOpacity>
        </View>

        {/* Footer with editing tools based on active tab */}
        <View style={styles.footer}>
          {activeTab === 'crop' && (
            <View style={styles.toolsContainer}>
              <View style={styles.cropMessageContainer}>
                <Text style={styles.footerText}>
                  Drag corners to resize or drag center to move
                </Text>

                {/* Manual crop message */}
                <View style={styles.manualCropContainer}>
                  <TouchableOpacity
                    style={[
                      styles.aspectRatioButton,
                      styles.activeAspectRatioButton
                    ]}
                  >
                    <Ionicons
                      name="crop-outline"
                      size={20}
                      color="#ffffff"
                    />
                    <Text style={[
                      styles.aspectRatioText,
                      styles.activeAspectRatioText
                    ]}>Manual Crop</Text>
                  </TouchableOpacity>
                </View>

                <TouchableOpacity
                  style={styles.cropButton}
                  onPress={previewTransformations}
                >
                  <Ionicons name="checkmark-circle" size={24} color="#ffffff" />
                  <Text style={styles.cropButtonText}>Apply Crop</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}

          {activeTab === 'rotate' && (
            <View style={styles.toolsContainer}>
              <TouchableOpacity style={styles.toolButton} onPress={rotateLeft}>
                <Ionicons name="arrow-undo" size={28} color="#ffffff" />
                <Text style={styles.toolButtonText}>Rotate Left</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.toolButton} onPress={rotateRight}>
                <Ionicons name="arrow-redo" size={28} color="#ffffff" />
                <Text style={styles.toolButtonText}>Rotate Right</Text>
              </TouchableOpacity>
            </View>
          )}

          {activeTab === 'flip' && (
            <View style={styles.toolsContainer}>
              <TouchableOpacity
                style={[styles.toolButton, flipHorizontal && styles.activeToolButton]}
                onPress={toggleFlipHorizontal}
              >
                <Ionicons name="swap-horizontal" size={28} color={flipHorizontal ? "#ffffff" : "#bbbbbb"} />
                <Text style={[styles.toolButtonText, flipHorizontal && styles.activeToolButtonText]}>Flip Horizontal</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.toolButton, flipVertical && styles.activeToolButton]}
                onPress={toggleFlipVertical}
              >
                <Ionicons name="swap-vertical" size={28} color={flipVertical ? "#ffffff" : "#bbbbbb"} />
                <Text style={[styles.toolButtonText, flipVertical && styles.activeToolButtonText]}>Flip Vertical</Text>
              </TouchableOpacity>
            </View>
          )}

          {/* Preview button - show when any changes have been made */}
          {(rotation !== 0 || flipHorizontal || flipVertical || activeTab === 'crop') && (
            <TouchableOpacity
              style={[
                styles.previewButton,
                editedImageUri ? styles.previewButtonApplied : {}
              ]}
              onPress={previewTransformations}
            >
              <Text style={styles.previewButtonText}>
                {editedImageUri ? 'Update Changes' : 'Apply Changes'}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#151515', // Slightly lighter than pure black for better contrast
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 50 : 20,
    paddingBottom: 15,
    backgroundColor: '#1a1a1a', // Slightly lighter than the main background
    borderBottomWidth: 1,
    borderBottomColor: '#333333', // Subtle border for better definition
    elevation: 5, // For Android shadow
    shadowColor: '#000', // For iOS shadow
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  headerButton: {
    padding: 10,
    backgroundColor: '#252525', // Slightly lighter background for better visibility
    borderRadius: 5,
    paddingHorizontal: 12,
  },
  headerButtonText: {
    color: '#ffffff',
    fontSize: 17,
  },
  doneButton: {
    fontWeight: 'bold',
  },
  headerTitle: {
    color: '#ffffff',
    fontSize: 17,
    fontWeight: 'bold',
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#101010', // Darker than the container background for contrast
  },
  image: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT * 0.5, // Reduced height to make room for tabs
  },
  cropOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.65)', // Darker overlay for better contrast with the crop box
  },
  cropBox: {
    borderWidth: 2, // Thicker border for better visibility
    borderColor: '#ffffff', // Blue color to match the app's theme
    backgroundColor: 'rgba(255, 255, 255, 0.05)', // Slightly visible background for better contrast
    position: 'absolute',
  },
  cropCorner: {
    position: 'absolute',
    top: -10,
    left: -10,
    width: 20, // Larger corner for better visibility and touch target
    height: 20, // Larger corner for better visibility and touch target
    borderColor: '#ffffff', // Blue color to match the app's theme
    borderTopWidth: 3, // Thicker border for better visibility
    borderLeftWidth: 3, // Thicker border for better visibility
    backgroundColor: 'rgba(79, 195, 247, 0.3)', // Semi-transparent blue background
    borderRadius: 3, // Slightly rounded corners
  },
  topRight: {
    left: 'auto',
    right: -10,
    borderLeftWidth: 0,
    borderRightWidth: 3, // Thicker border to match the other corners
  },
  bottomLeft: {
    top: 'auto',
    bottom: -10,
    borderTopWidth: 0,
    borderBottomWidth: 3, // Thicker border to match the other corners
  },
  bottomRight: {
    top: 'auto',
    left: 'auto',
    right: -10,
    bottom: -10,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    borderRightWidth: 3, // Thicker border to match the other corners
    borderBottomWidth: 3, // Thicker border to match the other corners
  },
  // Grid lines for the crop box
  gridLine: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  horizontalGridLine: {
    width: '100%',
    height: 1,
    top: '33.33%',
    left: 0,
    right: 0,
    bottom: 'auto',
  },
  verticalGridLine: {
    width: 1,
    height: '100%',
    left: '33.33%',
    top: 0,
    bottom: 0,
    right: 'auto',
  },
  // Tabs container
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: '#1a1a1a',
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#333333',
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  activeTab: {
    borderBottomWidth: 3,
    borderBottomColor: '#ffffff',
    backgroundColor: '#252525',
  },
  tabText: {
    color: '#ffffff',
    marginLeft: 5,
    fontSize: 15,
  },
  activeTabText: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  // Footer and tools
  footer: {
    padding: 20,
    backgroundColor: '#1a1a1a', // Slightly lighter than the main background to match the header
    borderTopWidth: 1,
    borderTopColor: '#333333', // Subtle border for better definition
    elevation: 5, // For Android shadow
    shadowColor: '#000', // For iOS shadow
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  footerText: {
    color: '#bbbbbb', // Lighter color for better visibility
    textAlign: 'center',
    fontSize: 15, // Slightly larger for better readability
  },
  toolsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 10,
  },
  cropMessageContainer: {
    width: '100%',
    alignItems: 'center',
  },
  aspectRatioContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginTop: 15,
    marginBottom: 10,
  },
  manualCropContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    marginTop: 15,
    marginBottom: 10,
  },
  aspectRatioButton: {
    alignItems: 'center',
    padding: 8,
    borderRadius: 5,
    backgroundColor: '#252525',
    minWidth: 60,
  },
  activeAspectRatioButton: {
    backgroundColor: '#2a3a45',
    borderWidth: 1,
    borderColor: '#ffffff',
  },
  aspectRatioText: {
    color: '#bbbbbb',
    fontSize: 12,
    marginTop: 4,
  },
  activeAspectRatioText: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  cropButton: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 5,
    marginTop: 15,
  },
  cropButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  toolButton: {
    alignItems: 'center',
    padding: 10,
    borderRadius: 5,
    backgroundColor: '#252525',
    minWidth: 120,
  },
  activeToolButton: {
    backgroundColor: '#2a3a45', // Slightly blue tinted background when active
    borderWidth: 1,
    borderColor: '#ffffff',
  },
  toolButtonText: {
    color: '#bbbbbb',
    marginTop: 5,
    fontSize: 14,
  },
  activeToolButtonText: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  previewButton: {
    backgroundColor: '#ffffff',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 5,
    alignItems: 'center',
    marginTop: 15,
    alignSelf: 'center',
    minWidth: 180,
  },
  previewButtonApplied: {
    backgroundColor: '#2a3a45',
    borderWidth: 1,
    borderColor: '#ffffff',
  },
  previewButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ImageEditor;

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Linking,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSettings } from '../contexts/SettingsContext';

const BluetoothStatusPopup = ({ 
  visible, 
  onClose, 
  isBluetoothEnabled, 
  permissionsGranted 
}) => {
  const { darkModeEnabled } = useSettings();

  const handleOpenSettings = () => {
    if (Platform.OS === 'android') {
      Linking.openSettings();
    } else {
      // On iOS, we can't directly open Bluetooth settings
      Linking.openURL('App-Prefs:Bluetooth');
    }
    onClose();
  };

  const getBluetoothStatusInfo = () => {
    if (isBluetoothEnabled) {
      return {
        icon: 'checkmark-circle',
        color: '#4caf50',
        title: 'Bluetooth is Enabled',
        message: 'Your device\'s Bluetooth is turned on and ready to connect to EEG devices.'
      };
    } else {
      return {
        icon: 'close-circle',
        color: '#f44336',
        title: 'Bluetooth is Disabled',
        message: 'Please enable Bluetooth in your device settings to scan for and connect to EEG devices.'
      };
    }
  };

  const getPermissionStatusInfo = () => {
    if (permissionsGranted) {
      return {
        icon: 'checkmark-circle',
        color: '#4caf50',
        title: 'Permissions Granted',
        message: 'Synaptix has the necessary Bluetooth permissions to function properly.'
      };
    } else {
      return {
        icon: 'close-circle',
        color: '#f44336',
        title: 'Permissions Required',
        message: 'Synaptix needs Bluetooth permissions to scan for and connect to EEG devices.'
      };
    }
  };

  const bluetoothStatus = getBluetoothStatusInfo();
  const permissionStatus = getPermissionStatusInfo();

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={[
          styles.popup,
          { 
            backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff',
            borderColor: darkModeEnabled ? '#333333' : '#e0e0e0'
          }
        ]}>
          {/* Header */}
          <View style={styles.header}>
            <View style={[
              styles.iconContainer,
              { backgroundColor: darkModeEnabled ? '#2d1b69' : '#e8eaf6' }
            ]}>
              <Ionicons
                name="bluetooth"
                size={32}
                color={darkModeEnabled ? '#bb86fc' : '#5e35b1'}
              />
            </View>
            <Text style={[
              styles.title,
              { color: darkModeEnabled ? '#ffffff' : '#121212' }
            ]}>
              Bluetooth Status
            </Text>
          </View>

          {/* Bluetooth Status */}
          <View style={styles.statusSection}>
            <View style={styles.statusItem}>
              <Ionicons
                name={bluetoothStatus.icon}
                size={24}
                color={bluetoothStatus.color}
              />
              <View style={styles.statusContent}>
                <Text style={[
                  styles.statusTitle,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>
                  {bluetoothStatus.title}
                </Text>
                <Text style={[
                  styles.statusMessage,
                  { color: darkModeEnabled ? '#e0e0e0' : '#666666' }
                ]}>
                  {bluetoothStatus.message}
                </Text>
              </View>
            </View>
          </View>

          {/* Permission Status */}
          <View style={styles.statusSection}>
            <View style={styles.statusItem}>
              <Ionicons
                name={permissionStatus.icon}
                size={24}
                color={permissionStatus.color}
              />
              <View style={styles.statusContent}>
                <Text style={[
                  styles.statusTitle,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>
                  {permissionStatus.title}
                </Text>
                <Text style={[
                  styles.statusMessage,
                  { color: darkModeEnabled ? '#e0e0e0' : '#666666' }
                ]}>
                  {permissionStatus.message}
                </Text>
              </View>
            </View>
          </View>

          {/* Actions */}
          <View style={styles.actions}>
            {!isBluetoothEnabled && (
              <TouchableOpacity
                style={[
                  styles.primaryButton,
                  { backgroundColor: darkModeEnabled ? '#bb86fc' : '#9c27b0' }
                ]}
                onPress={handleOpenSettings}
              >
                <Ionicons
                  name="settings-outline"
                  size={18}
                  color="#ffffff"
                  style={styles.buttonIcon}
                />
                <Text style={styles.primaryButtonText}>
                  Open Settings
                </Text>
              </TouchableOpacity>
            )}
            
            <TouchableOpacity
              style={[
                styles.secondaryButton,
                { 
                  borderColor: darkModeEnabled ? '#666666' : '#cccccc',
                  backgroundColor: 'transparent'
                }
              ]}
              onPress={onClose}
            >
              <Text style={[
                styles.secondaryButtonText,
                { color: darkModeEnabled ? '#e0e0e0' : '#666666' }
              ]}>
                Close
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  popup: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  statusSection: {
    marginBottom: 20,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 8,
  },
  statusContent: {
    flex: 1,
    marginLeft: 12,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  statusMessage: {
    fontSize: 14,
    lineHeight: 20,
  },
  actions: {
    gap: 12,
    marginTop: 8,
  },
  primaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  buttonIcon: {
    marginRight: 8,
  },
  primaryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default BluetoothStatusPopup;

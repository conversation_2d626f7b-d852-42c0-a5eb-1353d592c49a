import React from 'react';
import { TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useNavigationState } from '@react-navigation/native';
import { useAuth } from '../contexts/AuthContext';
import { useSettings } from '../contexts/SettingsContext';

export default function ProfileHeaderButton() {
  const navigation = useNavigation();
  const { currentUser } = useAuth();
  const { darkModeEnabled } = useSettings();

  const handlePress = () => {
    try {
      navigation.navigate('UserProfile');
    } catch (error) {
      console.error('Navigation error:', error);
      // Try to navigate to the parent navigator first
      try {
        navigation.navigate('Tabs', { screen: 'UserProfile' });
      } catch (innerError) {
        console.error('Second navigation error:', innerError);
      }
    }
  };

  return (
    <TouchableOpacity
      style={{ marginRight: 15 }}
      onPress={handlePress}
    >
      {currentUser?.profilePicture ? (
        <Image
          source={{ uri: currentUser.profilePicture }}
          style={{ width: 30, height: 30, borderRadius: 15 }}
        />
      ) : (
        <Ionicons
          name="person-circle"
          size={30}
          color={darkModeEnabled ? "#cccccc" : "#666666"} // Light white in dark mode, dark grey in light mode
        />
      )}
    </TouchableOpacity>
  );
}

com.synaptix.app-emoji2-views-helper-1.3.0-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\029e2af128490e615d059bebc021f478\transformed\emoji2-views-helper-1.3.0\res
com.synaptix.app-lifecycle-runtime-ktx-2.6.2-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\076e7c8d86f4ae165701d57613b55ad7\transformed\lifecycle-runtime-ktx-2.6.2\res
com.synaptix.app-fragment-1.6.1-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\0e259728d88428d0a6fd4fb3d2f819b3\transformed\fragment-1.6.1\res
com.synaptix.app-expo.modules.imagepicker-16.1.4-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\res
com.synaptix.app-firebase-common-21.0.0-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\res
com.synaptix.app-core-ktx-1.13.1-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\1ebe30aeca87bb2621bf1e5abb9d628c\transformed\core-ktx-1.13.1\res
com.synaptix.app-glide-4.16.0-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\232d413dddcf7d05a9a043f886f3aacc\transformed\glide-4.16.0\res
com.synaptix.app-lifecycle-viewmodel-savedstate-2.6.2-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\2984cc02f72a737058cfeca27fb04878\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.synaptix.app-expo.modules.securestore-14.2.3-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\2c0d32a83122b867e33b8516cbb12905\transformed\expo.modules.securestore-14.2.3\res
com.synaptix.app-lifecycle-livedata-core-ktx-2.6.2-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\3c452e07798d5e01f66963b4329b14cc\transformed\lifecycle-livedata-core-ktx-2.6.2\res
com.synaptix.app-core-runtime-2.2.0-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\4034b4b1ab8419396693267014837625\transformed\core-runtime-2.2.0\res
com.synaptix.app-savedstate-1.2.1-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\4a97664ae2368e45ad054d43ee02ac19\transformed\savedstate-1.2.1\res
com.synaptix.app-appcompat-resources-1.7.0-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\4ec572916ea1f129e0942696847a1448\transformed\appcompat-resources-1.7.0\res
com.synaptix.app-transition-1.5.0-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\540a2f03ddcc6a751649be6050a9c6d1\transformed\transition-1.5.0\res
com.synaptix.app-lifecycle-viewmodel-ktx-2.6.2-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\55c65349e9f76a371a924d8b223cc8c7\transformed\lifecycle-viewmodel-ktx-2.6.2\res
com.synaptix.app-expo.modules.notifications-0.31.3-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\res
com.synaptix.app-fragment-ktx-1.6.1-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\5972ddf3f9cbc9412d6a8e83292e1a6d\transformed\fragment-ktx-1.6.1\res
com.synaptix.app-material-1.12.0-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\6550f91160cccf4761958ba318c6d009\transformed\material-1.12.0\res
com.synaptix.app-lifecycle-runtime-2.6.2-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5ef8d394a6c24184eafbdfe860a06e\transformed\lifecycle-runtime-2.6.2\res
com.synaptix.app-emoji2-1.3.0-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\res
com.synaptix.app-drawee-3.6.0-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\7133ea62f60e2d183b5e312dbc30a8d5\transformed\drawee-3.6.0\res
com.synaptix.app-lifecycle-livedata-core-2.6.2-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\7470730cec1e0826f1d938b05924fd06\transformed\lifecycle-livedata-core-2.6.2\res
com.synaptix.app-lifecycle-viewmodel-2.6.2-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\7876c15d01538d67cc2c4103d1dcafe1\transformed\lifecycle-viewmodel-2.6.2\res
com.synaptix.app-startup-runtime-1.1.1-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\7c55689459677533d880d5770c041a5a\transformed\startup-runtime-1.1.1\res
com.synaptix.app-react-android-0.79.4-debug-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed22a0039ae602719136455601f8f0f\transformed\react-android-0.79.4-debug\res
com.synaptix.app-media-1.0.0-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\9194d47ce755d1ac815ee8da7b1e34ff\transformed\media-1.0.0\res
com.synaptix.app-core-1.13.1-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\res
com.synaptix.app-browser-1.6.0-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\9a19d447f99dacc987e9045d6bd5cdca\transformed\browser-1.6.0\res
com.synaptix.app-lifecycle-process-2.6.2-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\res
com.synaptix.app-BlurView-version-2.0.6-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\9eac3419205ecd6b0de0e82b7ca58ec1\transformed\BlurView-version-2.0.6\res
com.synaptix.app-tracing-ktx-1.2.0-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\a110528b690b862fb4278e561fbf4930\transformed\tracing-ktx-1.2.0\res
com.synaptix.app-swiperefreshlayout-1.1.0-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\a5f356aedc74e44a86f4294ab019421b\transformed\swiperefreshlayout-1.1.0\res
com.synaptix.app-activity-1.10.0-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\a8bbdaf5a2c5874265e973dea75a58a7\transformed\activity-1.10.0\res
com.synaptix.app-biometric-1.1.0-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\ac4000ff40bdee0ae650966147dfc40c\transformed\biometric-1.1.0\res
com.synaptix.app-lifecycle-livedata-2.6.2-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\b4998a702a63fa929eca66b6b57624d0\transformed\lifecycle-livedata-2.6.2\res
com.synaptix.app-tracing-1.2.0-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\bf251b680b4c0081841a858beaef439f\transformed\tracing-1.2.0\res
com.synaptix.app-recyclerview-1.1.0-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\c184311a2cea37320ef6afbc4a0aa502\transformed\recyclerview-1.1.0\res
com.synaptix.app-constraintlayout-2.0.1-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\c26aaa9d43f6c32d0679ac0a055837c8\transformed\constraintlayout-2.0.1\res
com.synaptix.app-autofill-1.1.0-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\c7bc0d3483dd2faa77540c8e42cad49c\transformed\autofill-1.1.0\res
com.synaptix.app-lifecycle-service-2.6.2-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\ca79320bf5be63e572f5185dbdb89374\transformed\lifecycle-service-2.6.2\res
com.synaptix.app-play-services-base-18.0.1-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\d29e10314c2a4b4c7355c13794550e06\transformed\play-services-base-18.0.1\res
com.synaptix.app-profileinstaller-1.4.0-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\res
com.synaptix.app-savedstate-ktx-1.2.1-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\d52c62331fda403e7b59eb4e693e28be\transformed\savedstate-ktx-1.2.1\res
com.synaptix.app-firebase-messaging-24.0.1-43 C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\res
com.synaptix.app-activity-ktx-1.10.0-44 C:\Users\<USER>\.gradle\caches\8.13\transforms\e045233b2a7ad681066b55c3cefd4d13\transformed\activity-ktx-1.10.0\res
com.synaptix.app-viewpager2-1.0.0-45 C:\Users\<USER>\.gradle\caches\8.13\transforms\e1bf76f9dc3a11f29be9edef53e9c03c\transformed\viewpager2-1.0.0\res
com.synaptix.app-cardview-1.0.0-46 C:\Users\<USER>\.gradle\caches\8.13\transforms\e78ab6e66fc9e7893b1d10deceb25bd2\transformed\cardview-1.0.0\res
com.synaptix.app-coordinatorlayout-1.2.0-47 C:\Users\<USER>\.gradle\caches\8.13\transforms\e7c52fddb3f71df7c52c6d2210bdbf0f\transformed\coordinatorlayout-1.2.0\res
com.synaptix.app-play-services-basement-18.3.0-48 C:\Users\<USER>\.gradle\caches\8.13\transforms\ef88a174ce7b920cee45b0a5e12a3fac\transformed\play-services-basement-18.3.0\res
com.synaptix.app-annotation-experimental-1.4.0-49 C:\Users\<USER>\.gradle\caches\8.13\transforms\f1450e4ef69b40f313f4440b3ddafbeb\transformed\annotation-experimental-1.4.0\res
com.synaptix.app-android-image-cropper-4.6.0-50 C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\res
com.synaptix.app-appcompat-1.7.0-51 C:\Users\<USER>\.gradle\caches\8.13\transforms\f64dc84a821f94336e410d15a520d5cf\transformed\appcompat-1.7.0\res
com.synaptix.app-drawerlayout-1.1.1-52 C:\Users\<USER>\.gradle\caches\8.13\transforms\fa90bc2a178b0b5f87435f59781d1b83\transformed\drawerlayout-1.1.1\res
com.synaptix.app-pngs-53 C:\Users\<USER>\Projects\Synaptix\android\app\build\generated\res\pngs\debug
com.synaptix.app-resValues-54 C:\Users\<USER>\Projects\Synaptix\android\app\build\generated\res\resValues\debug
com.synaptix.app-packageDebugResources-55 C:\Users\<USER>\Projects\Synaptix\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.synaptix.app-packageDebugResources-56 C:\Users\<USER>\Projects\Synaptix\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.synaptix.app-debug-57 C:\Users\<USER>\Projects\Synaptix\android\app\build\intermediates\merged_res\debug\mergeDebugResources
com.synaptix.app-debug-58 C:\Users\<USER>\Projects\Synaptix\android\app\src\debug\res
com.synaptix.app-main-59 C:\Users\<USER>\Projects\Synaptix\android\app\src\main\res
com.synaptix.app-debug-60 C:\Users\<USER>\Projects\Synaptix\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-61 C:\Users\<USER>\Projects\Synaptix\node_modules\@react-native-community\datetimepicker\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-62 C:\Users\<USER>\Projects\Synaptix\node_modules\@react-native-picker\picker\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-63 C:\Users\<USER>\Projects\Synaptix\node_modules\expo-constants\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-64 C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-client\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-65 C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-launcher\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-66 C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-menu-interface\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-67 C:\Users\<USER>\Projects\Synaptix\node_modules\expo-dev-menu\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-68 C:\Users\<USER>\Projects\Synaptix\node_modules\expo-file-system\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-69 C:\Users\<USER>\Projects\Synaptix\node_modules\expo-image-loader\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-70 C:\Users\<USER>\Projects\Synaptix\node_modules\expo-image-manipulator\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-71 C:\Users\<USER>\Projects\Synaptix\node_modules\expo-json-utils\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-72 C:\Users\<USER>\Projects\Synaptix\node_modules\expo-manifests\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-73 C:\Users\<USER>\Projects\Synaptix\node_modules\expo-modules-core\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-74 C:\Users\<USER>\Projects\Synaptix\node_modules\expo-updates-interface\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-75 C:\Users\<USER>\Projects\Synaptix\node_modules\expo\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-76 C:\Users\<USER>\Projects\Synaptix\node_modules\react-native-ble-plx\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-77 C:\Users\<USER>\Projects\Synaptix\node_modules\react-native-get-random-values\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-78 C:\Users\<USER>\Projects\Synaptix\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-79 C:\Users\<USER>\Projects\Synaptix\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug\packageDebugResources
com.synaptix.app-debug-80 C:\Users\<USER>\Projects\Synaptix\node_modules\react-native-svg\android\build\intermediates\packaged_res\debug\packageDebugResources

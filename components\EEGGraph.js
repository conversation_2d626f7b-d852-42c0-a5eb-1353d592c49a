import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import { useSettings } from '../contexts/SettingsContext';

const screenWidth = Dimensions.get("window").width;

/**
 * EEGGraph Component
 * 
 * Displays real-time EEG data in a scrolling line chart
 */
const EEGGraph = ({ 
  eegData, 
  isConnected = false, 
  deviceName = 'Unknown Device',
  maxDataPoints = 50 
}) => {
  const { darkModeEnabled } = useSettings();
  const [displayData, setDisplayData] = useState([]);
  const [isSimulating, setIsSimulating] = useState(false);
  const simulationInterval = useRef(null);

  // Process incoming EEG data
  useEffect(() => {
    if (eegData && eegData.channels && eegData.channels.length > 0) {
      // Use real EEG data - process multiple channels or single channel
      let dataPoint;

      if (eegData.channels.length === 1) {
        dataPoint = eegData.channels[0];
      } else {
        // For multiple channels, use the average or the first channel
        dataPoint = eegData.channels.reduce((sum, val) => sum + val, 0) / eegData.channels.length;
      }

      // Normalize the data point to a reasonable range for visualization (-100 to 100)
      // Assuming EEG values are typically in the range 0-255 or similar
      let normalizedPoint;
      if (dataPoint > 1000) {
        // Large values - scale down significantly
        normalizedPoint = ((dataPoint - 2048) / 2048) * 100;
      } else {
        // Standard range - use typical EEG normalization
        normalizedPoint = ((dataPoint - 128) / 128) * 100;
      }

      // Clamp to reasonable bounds
      normalizedPoint = Math.max(-100, Math.min(100, normalizedPoint));

      setDisplayData(prevData => {
        const newData = [...prevData, normalizedPoint];
        return newData.slice(-maxDataPoints); // Keep only the last N points
      });

      // Stop simulation if we're getting real data
      if (isSimulating) {
        setIsSimulating(false);
        if (simulationInterval.current) {
          clearInterval(simulationInterval.current);
          simulationInterval.current = null;
        }
      }
    } else if (isConnected && !isSimulating) {
      // Start simulation if connected but no real data
      startSimulation();
    }
  }, [eegData, isConnected, isSimulating, maxDataPoints]);

  // Start EEG simulation when connected but no real data
  const startSimulation = () => {
    setIsSimulating(true);
    let timeOffset = 0;

    simulationInterval.current = setInterval(() => {
      // Generate realistic EEG-like waveform with multiple frequency components
      const time = timeOffset / 10; // Slower time progression for better visualization

      // Different brain wave frequencies
      const delta = Math.sin(time * 2 * Math.PI * 2) * 20;   // 2 Hz delta waves (deep sleep)
      const theta = Math.sin(time * 2 * Math.PI * 6) * 25;   // 6 Hz theta waves (meditation)
      const alpha = Math.sin(time * 2 * Math.PI * 10) * 30;  // 10 Hz alpha waves (relaxed)
      const beta = Math.sin(time * 2 * Math.PI * 20) * 15;   // 20 Hz beta waves (active thinking)
      const gamma = Math.sin(time * 2 * Math.PI * 40) * 8;   // 40 Hz gamma waves (high cognition)

      // Add some random noise and artifacts
      const noise = (Math.random() - 0.5) * 8;
      const artifact = Math.random() < 0.02 ? (Math.random() - 0.5) * 50 : 0; // Occasional artifacts

      // Combine all components
      const eegValue = delta + theta + alpha + beta + gamma + noise + artifact;

      setDisplayData(prevData => {
        const newData = [...prevData, eegValue];
        return newData.slice(-maxDataPoints);
      });

      timeOffset++;
    }, 100); // Update every 100ms for smooth animation
  };

  // Cleanup simulation on unmount
  useEffect(() => {
    return () => {
      if (simulationInterval.current) {
        clearInterval(simulationInterval.current);
      }
    };
  }, []);

  // Stop simulation when disconnected
  useEffect(() => {
    if (!isConnected && simulationInterval.current) {
      clearInterval(simulationInterval.current);
      simulationInterval.current = null;
      setIsSimulating(false);
      setDisplayData([]);
    }
  }, [isConnected]);

  // Chart configuration
  const chartConfig = {
    backgroundColor: darkModeEnabled ? '#121212' : '#ffffff',
    backgroundGradientFrom: darkModeEnabled ? '#1e1e1e' : '#f5f5f5',
    backgroundGradientTo: darkModeEnabled ? '#1e1e1e' : '#f5f5f5',
    decimalPlaces: 1,
    color: (opacity = 1) => darkModeEnabled 
      ? `rgba(156, 39, 176, ${opacity})` // Purple for dark mode
      : `rgba(76, 175, 80, ${opacity})`, // Green for light mode
    labelColor: (opacity = 1) => darkModeEnabled 
      ? `rgba(255, 255, 255, ${opacity})` 
      : `rgba(0, 0, 0, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: "0", // Hide dots for smoother line
    },
    propsForBackgroundLines: {
      strokeDasharray: "", // Solid grid lines
      stroke: darkModeEnabled ? "#333333" : "#e0e0e0",
      strokeWidth: 1,
    },
  };

  // Prepare chart data
  const chartData = {
    labels: [], // No labels for cleaner look
    datasets: [
      {
        data: displayData.length > 0 ? displayData : [0],
        color: (opacity = 1) => darkModeEnabled 
          ? `rgba(156, 39, 176, ${opacity})` 
          : `rgba(76, 175, 80, ${opacity})`,
        strokeWidth: 2,
      }
    ]
  };

  return (
    <View style={styles.container}>
      {/* Graph Header */}
      <View style={styles.header}>
        <Text style={[
          styles.title,
          { color: darkModeEnabled ? '#ffffff' : '#333333' }
        ]}>
          EEG Real-Time Monitor
        </Text>
        
        {isConnected && (
          <View style={styles.statusContainer}>
            <View style={[
              styles.statusDot,
              { backgroundColor: displayData.length > 0 ? '#4CAF50' : '#FFC107' }
            ]} />
            <Text style={[
              styles.statusText,
              { color: darkModeEnabled ? '#e0e0e0' : '#666666' }
            ]}>
              {displayData.length > 0 
                ? (eegData && eegData.channels ? 'Live Data' : 'Simulated') 
                : 'Connecting...'
              }
            </Text>
          </View>
        )}
      </View>

      {/* EEG Chart */}
      {isConnected ? (
        <LineChart
          data={chartData}
          width={screenWidth - 40}
          height={220}
          chartConfig={chartConfig}
          bezier={false} // Use straight lines for EEG data
          style={[
            styles.chart,
            { 
              backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff',
              borderColor: darkModeEnabled ? '#333333' : '#e0e0e0',
              borderWidth: 1,
            }
          ]}
          withHorizontalLabels={true}
          withVerticalLabels={false}
          withDots={false}
          withShadow={false}
          withScrollableDot={false}
        />
      ) : (
        <View style={[
          styles.noDataContainer,
          { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#f5f5f5' }
        ]}>
          <Text style={[
            styles.noDataText,
            { color: darkModeEnabled ? '#9e9e9e' : '#757575' }
          ]}>
            Connect to a device to view EEG data
          </Text>
        </View>
      )}

      {/* Data Info */}
      {displayData.length > 0 && (
        <View style={styles.dataInfo}>
          <Text style={[
            styles.dataInfoText,
            { color: darkModeEnabled ? '#e0e0e0' : '#666666' }
          ]}>
            Current: {displayData[displayData.length - 1]?.toFixed(1) || '0.0'} μV
          </Text>
          <Text style={[
            styles.dataInfoText,
            { color: darkModeEnabled ? '#e0e0e0' : '#666666' }
          ]}>
            Samples: {displayData.length}/{maxDataPoints}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
    marginHorizontal: 20,
  },
  noDataContainer: {
    height: 220,
    marginHorizontal: 20,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  noDataText: {
    fontSize: 16,
    fontWeight: '500',
  },
  dataInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    marginTop: 10,
  },
  dataInfoText: {
    fontSize: 12,
    fontWeight: '500',
  },
});

export default EEGGraph;

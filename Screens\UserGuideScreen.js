import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import CustomHeader from '../components/CustomHeader';
import { ensureStatusBarAppearance } from '../utils/screenTransition';
import { useSettings } from '../contexts/SettingsContext';

export default function UserGuideScreen() {
  const navigation = useNavigation();
  const { darkModeEnabled } = useSettings();

  // Ensure StatusBar is properly configured when the screen mounts
  useEffect(() => {
    ensureStatusBarAppearance(darkModeEnabled);
  }, [darkModeEnabled]);

  const handleBackPress = () => {
    navigation.goBack();
  };

  return (
    <View style={[
      styles.container,
      { backgroundColor: darkModeEnabled ? '#121212' : '#f5f5f5' }
    ]}>
      <StatusBar barStyle={darkModeEnabled ? "light-content" : "dark-content"} />
      <CustomHeader
        title="User Guide"
        showProfileIcon={false}
        showBackButton={true}
        onBackPress={handleBackPress} />

      <ScrollView style={styles.scrollContainer}>
        <View style={styles.guideContainer}>
          <View style={[
            styles.guideCard,
            { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
          ]}>
            <View style={[
              styles.sectionHeader,
              { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
            ]}>
              <Text style={[
                styles.sectionTitle,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>Getting Started</Text>
            </View>

            <View style={styles.guideItem}>
              <View style={[
                styles.iconContainer,
                { backgroundColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
              ]}>
                <Ionicons
                  name="home-outline"
                  size={24}
                  color={darkModeEnabled ? "#ffffff" : "#121212"}
                />
              </View>
              <View style={styles.guideContent}>
                <Text style={[
                  styles.guideTitle,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>Home Screen</Text>
                <Text style={[
                  styles.guideDescription,
                  { color: darkModeEnabled ? '#cccccc' : '#666666' }
                ]}>
                  The home screen provides an overview of the app's main features and your current status.
                </Text>
              </View>
            </View>

            <View style={styles.guideItem}>
              <View style={[
                styles.iconContainer,
                { backgroundColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
              ]}>
                <Ionicons
                  name="person-outline"
                  size={24}
                  color={darkModeEnabled ? "#ffffff" : "#121212"}
                />
              </View>
              <View style={styles.guideContent}>
                <Text style={[
                  styles.guideTitle,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>Profile</Text>
                <Text style={[
                  styles.guideDescription,
                  { color: darkModeEnabled ? '#cccccc' : '#666666' }
                ]}>
                  Access your profile by tapping the profile icon in the top right corner of the home screen.
                  Here you can view and edit your personal information and access settings.
                </Text>
              </View>
            </View>

            <View style={styles.guideItem}>
              <View style={[
                styles.iconContainer,
                { backgroundColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
              ]}>
                <Ionicons
                  name="add-circle-outline"
                  size={24}
                  color={darkModeEnabled ? "#ffffff" : "#121212"}
                />
              </View>
              <View style={styles.guideContent}>
                <Text style={[
                  styles.guideTitle,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>Connect</Text>
                <Text style={[
                  styles.guideDescription,
                  { color: darkModeEnabled ? '#cccccc' : '#666666' }
                ]}>
                  Use the connect feature to establish a Bluetooth connection with your device. Scan for available
                  devices, select your device from the list, and click connect.
                </Text>
              </View>
            </View>
          </View>

          <View style={[
            styles.guideCard,
            { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
          ]}>
            <View style={[
              styles.sectionHeader,
              { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
            ]}>
              <Text style={[
                styles.sectionTitle,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>Features</Text>
            </View>

            <View style={styles.guideItem}>
              <View style={[
                styles.iconContainer,
                { backgroundColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
              ]}>
                <Ionicons
                  name="settings-outline"
                  size={24}
                  color={darkModeEnabled ? "#ffffff" : "#121212"}
                />
              </View>
              <View style={styles.guideContent}>
                <Text style={[
                  styles.guideTitle,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>Settings</Text>
                <Text style={[
                  styles.guideDescription,
                  { color: darkModeEnabled ? '#cccccc' : '#666666' }
                ]}>
                  Access settings from your profile page. Here you can customize app preferences,
                  toggle notifications, and manage your account.
                </Text>
              </View>
            </View>

            <View style={styles.guideItem}>
              <View style={[
                styles.iconContainer,
                { backgroundColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
              ]}>
                <Ionicons
                  name="camera-outline"
                  size={24}
                  color={darkModeEnabled ? "#ffffff" : "#121212"}
                />
              </View>
              <View style={styles.guideContent}>
                <Text style={[
                  styles.guideTitle,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>Profile Picture</Text>
                <Text style={[
                  styles.guideDescription,
                  { color: darkModeEnabled ? '#cccccc' : '#666666' }
                ]}>
                  Add or change your profile picture by tapping on the profile image in your profile screen.
                  You can choose from your gallery or take a new photo.
                </Text>
              </View>
            </View>

            <View style={styles.guideItem}>
              <View style={[
                styles.iconContainer,
                { backgroundColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
              ]}>
                <Ionicons
                  name="analytics-outline"
                  size={24}
                  color={darkModeEnabled ? "#ffffff" : "#121212"}
                />
              </View>
              <View style={styles.guideContent}>
                <Text style={[
                  styles.guideTitle,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>Brain Analysis</Text>
                <Text style={[
                  styles.guideDescription,
                  { color: darkModeEnabled ? '#cccccc' : '#666666' }
                ]}>
                  After connecting your device, you can perform brain analysis to get insights into your neural patterns.
                </Text>
              </View>
            </View>
          </View>

          <View style={[
            styles.guideCard,
            { backgroundColor: darkModeEnabled ? '#1e1e1e' : '#ffffff' }
          ]}>
            <View style={[
              styles.sectionHeader,
              { borderBottomColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
            ]}>
              <Text style={[
                styles.sectionTitle,
                { color: darkModeEnabled ? '#ffffff' : '#121212' }
              ]}>Troubleshooting</Text>
            </View>

            <View style={styles.guideItem}>
              <View style={[
                styles.iconContainer,
                { backgroundColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
              ]}>
                <Ionicons
                  name="help-circle-outline"
                  size={24}
                  color={darkModeEnabled ? "#ffffff" : "#121212"}
                />
              </View>
              <View style={styles.guideContent}>
                <Text style={[
                  styles.guideTitle,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>Connection Issues</Text>
                <Text style={[
                  styles.guideDescription,
                  { color: darkModeEnabled ? '#cccccc' : '#666666' }
                ]}>
                  If you're having trouble connecting to your device, ensure it's powered on and within Bluetooth range.
                  Make sure Bluetooth is enabled on your phone and try scanning again. If problems persist, restart both your device and the app.
                </Text>
              </View>
            </View>

            <View style={styles.guideItem}>
              <View style={[
                styles.iconContainer,
                { backgroundColor: darkModeEnabled ? '#333333' : '#e0e0e0' }
              ]}>
                <Ionicons
                  name="refresh-outline"
                  size={24}
                  color={darkModeEnabled ? "#ffffff" : "#121212"}
                />
              </View>
              <View style={styles.guideContent}>
                <Text style={[
                  styles.guideTitle,
                  { color: darkModeEnabled ? '#ffffff' : '#121212' }
                ]}>App Performance</Text>
                <Text style={[
                  styles.guideDescription,
                  { color: darkModeEnabled ? '#cccccc' : '#666666' }
                ]}>
                  If the app is running slowly, try closing other applications on your device or restart the app.
                </Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  guideContainer: {
    padding: 20,
  },
  guideCard: {
    borderRadius: 10,
    padding: 20,
    marginBottom: 20,
  },
  sectionHeader: {
    borderBottomWidth: 1,
    paddingBottom: 15,
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  guideItem: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  guideContent: {
    flex: 1,
  },
  guideTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  guideDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
});

# Synaptix Flask API

This is a Flask API server for the Synaptix Brain App. It processes EEG data and returns prediction results.

## Setup

1. Install dependencies:
```
pip install -r requirements.txt
```

2. Run the server:
```
python app.py
```

The server will run on port 5000 by default. You can change this by setting the `PORT` environment variable.

## API Endpoints

### Health Check
```
GET /api/health
```
Returns the health status of the API.

### Predict
```
POST /api/predict
```
Processes EEG data and returns prediction results.

#### Request Body
```json
{
  "eegData": {
    "channels": [array of channel values],
    "timestamp": timestamp
  }
}
```

#### Response
```json
{
  "overallScore": number,
  "categories": [
    {
      "name": string,
      "score": number,
      "color": string
    }
  ],
  "recommendations": [array of strings]
}
```

## Deployment

This API can be deployed to any platform that supports Python, such as Heroku, AWS, or Google Cloud Platform.

For local development, you can run the server with:
```
python app.py
```

For production deployment, it's recommended to use Gunicorn:
```
gunicorn app:app
```
